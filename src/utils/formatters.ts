/**
 * Formats a number to Indonesian currency format with Rp prefix (Rp 1.000,00)
 * @param value - The number to format
 * @param decimals - Number of decimal places (default: 2)
 * @returns Formatted string with Rp prefix
 */
export const formatIDR = (value: number, decimals: number = 2): string => {
  // Convert to string with fixed decimal places
  const fixed = value.toFixed(decimals);

  // Split into whole and decimal parts
  const [whole, decimal] = fixed.split('.');

  // Format the whole part with dots as thousand separators
  const formattedWhole = whole.replace(/\B(?=(\d{3})+(?!\d))/g, '.');

  // Return the formatted number with Rp prefix and comma as decimal separator
  return `Rp ${formattedWhole}${decimal ? ',' + decimal : ''}`;
};

/**
 * Formats a number to Indonesian number format without currency prefix (1.000,00)
 * @param value - The number to format
 * @param decimals - Number of decimal places (default: 2)
 * @returns Formatted string without currency prefix
 */
export const formatNumber = (value: number, decimals: number = 2): string => {
  // Convert to string with fixed decimal places
  const fixed = value.toFixed(decimals);

  // Split into whole and decimal parts
  const [whole, decimal] = fixed.split('.');

  // Format the whole part with dots as thousand separators
  const formattedWhole = whole.replace(/\B(?=(\d{3})+(?!\d))/g, '.');

  // Return the formatted number with comma as decimal separator
  return `${formattedWhole}${decimal ? ',' + decimal : ''}`;
};
