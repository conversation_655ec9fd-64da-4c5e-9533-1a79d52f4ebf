import { supabase } from '../lib/supabase';

export interface Profile {
  id: string;
  firstName: string | null;
  lastName: string | null;
  fullName: string | null;
  avatarUrl: string | null;
  company: string | null;
  website: string | null;
  billingAddress: {
    street?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  } | null;
  createdAt: string;
  updatedAt: string;
}

export interface ProfileUpdateInput {
  firstName?: string | null;
  lastName?: string | null;
  avatarUrl?: string | null;
  company?: string | null;
  website?: string | null;
  billingAddress?: {
    street?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  } | null;
}

export const profileService = {
  async getProfile(): Promise<Profile | null> {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return null;
    }

    // With RLS enabled, this will only return the profile if it belongs to the authenticated user
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', session.user.id)
      .single();

    if (error) {
      console.error('Error fetching profile:', error);
      return null;
    }

    if (!data) return null;

    // Map database column names to our interface
    return {
      id: data.id,
      firstName: data.first_name,
      lastName: data.last_name,
      fullName: data.full_name,
      avatarUrl: data.avatar_url,
      company: data.company,
      website: data.website,
      billingAddress: data.billing_address,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };
  },

  async updateProfile(profile: ProfileUpdateInput): Promise<Profile | null> {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      throw new Error('User must be logged in to update profile');
    }

    // Map our interface to database column names
    const updateData = {
      first_name: profile.firstName,
      last_name: profile.lastName,
      avatar_url: profile.avatarUrl,
      company: profile.company,
      website: profile.website,
      billing_address: profile.billingAddress
    };

    // With RLS enabled, this will only update the profile if it belongs to the authenticated user
    const { data, error } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', session.user.id)
      .select()
      .single();

    if (error) {
      console.error('Error updating profile:', error);
      throw error;
    }

    // Map database column names to our interface
    return {
      id: data.id,
      firstName: data.first_name,
      lastName: data.last_name,
      fullName: data.full_name,
      avatarUrl: data.avatar_url,
      company: data.company,
      website: data.website,
      billingAddress: data.billing_address,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };
  },

  async updateUserMetadata(metadata: Record<string, any>): Promise<void> {
    const { error } = await supabase.auth.updateUser({
      data: metadata
    });

    if (error) {
      console.error('Error updating user metadata:', error);
      throw error;
    }
  }
};
