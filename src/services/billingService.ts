import { supabase } from '../lib/supabase';

export interface BillingPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  credits: number;
  isActive: boolean;
}

export interface UserBalance {
  id: number;
  userId: string;
  credits: number;
  createdAt: string;
  updatedAt: string;
}

export interface Transaction {
  id: number;
  userId: string;
  amount: number;
  description: string;
  transactionType: 'purchase' | 'usage' | 'refund' | 'bonus';
  referenceId?: string;
  createdAt: string;
}

export interface Payment {
  id: number;
  userId: string;
  amount: number;
  credits: number;
  status: 'completed' | 'pending' | 'failed';
  paymentMethod?: string;
  paymentReference?: string;
  createdAt: string;
  updatedAt: string;
}

export const billingService = {
  async getBillingPlans(): Promise<BillingPlan[]> {
    const { data, error } = await supabase
      .from('billing_plans')
      .select('*')
      .order('price', { ascending: true });

    if (error) {
      console.error('Error fetching billing plans:', error);
      throw error;
    }

    return (data || []).map(plan => ({
      id: plan.id,
      name: plan.name,
      description: plan.description,
      price: plan.price,
      credits: plan.credits,
      isActive: plan.is_active
    }));
  },

  async getUserBalance(): Promise<UserBalance | null> {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return null;
    }

    const { data, error } = await supabase
      .from('user_balances')
      .select('*')
      .eq('user_id', session.user.id)
      .single();

    if (error) {
      console.error('Error fetching user balance:', error);
      return null;
    }

    if (!data) return null;

    return {
      id: data.id,
      userId: data.user_id,
      credits: data.credits,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };
  },

  async getTransactions(limit: number = 10, offset: number = 0): Promise<{ data: Transaction[], count: number }> {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return { data: [], count: 0 };
    }

    // Get the data with pagination
    const { data, error, count } = await supabase
      .from('transactions')
      .select('*', { count: 'exact' })
      .eq('user_id', session.user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching transactions:', error);
      throw error;
    }

    const transactions = (data || []).map(transaction => ({
      id: transaction.id,
      userId: transaction.user_id,
      amount: transaction.amount,
      description: transaction.description,
      transactionType: transaction.transaction_type,
      referenceId: transaction.reference_id,
      createdAt: transaction.created_at
    }));

    return {
      data: transactions,
      count: count || 0
    };
  },

  async getPayments(limit: number = 10, offset: number = 0): Promise<Payment[]> {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return [];
    }

    const { data, error } = await supabase
      .from('payments')
      .select('*')
      .eq('user_id', session.user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching payments:', error);
      throw error;
    }

    return (data || []).map(payment => ({
      id: payment.id,
      userId: payment.user_id,
      amount: payment.amount,
      credits: payment.credits,
      status: payment.status,
      paymentMethod: payment.payment_method,
      paymentReference: payment.payment_reference,
      createdAt: payment.created_at,
      updatedAt: payment.updated_at
    }));
  },

  async createPayment(planId: string): Promise<{ success: boolean; error?: string; invoiceUrl?: string }> {
    try {
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        return { success: false, error: 'User not authenticated' };
      }

      // Get the plan details
      const { data: planData, error: planError } = await supabase
        .from('billing_plans')
        .select('*')
        .eq('id', planId)
        .single();

      if (planError || !planData) {
        return { success: false, error: 'Invalid plan selected' };
      }

      // Call the Supabase Edge Function to create a Xendit invoice
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/create-xendit-invoice`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          amount: planData.price,
          planId: planId,
          paymentMethod: 'xendit'
        })
      });

      const data = await response.json();

      if (!response.ok) {
        console.error('Error creating Xendit invoice:', data);
        return { success: false, error: data.error || 'Failed to create payment' };
      }

      // Return success with the invoice URL for redirection
      return {
        success: true,
        invoiceUrl: data.invoice_url
      };
    } catch (error) {
      console.error('Error creating payment:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  },

  // Function to check payment status
  async checkPaymentStatus(paymentId: number): Promise<{ status: 'completed' | 'pending' | 'failed'; error?: string }> {
    try {
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        return { status: 'failed', error: 'User not authenticated' };
      }

      const { data, error } = await supabase
        .from('payments')
        .select('status')
        .eq('id', paymentId)
        .eq('user_id', session.user.id)
        .single();

      if (error) {
        return { status: 'failed', error: 'Payment not found' };
      }

      return { status: data.status as 'completed' | 'pending' | 'failed' };
    } catch (error) {
      console.error('Error checking payment status:', error);
      return { status: 'failed', error: 'An unexpected error occurred' };
    }
  }
};
