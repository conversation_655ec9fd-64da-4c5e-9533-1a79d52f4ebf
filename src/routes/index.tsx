import React, { lazy, Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';

// Layouts
import MainLayout from '../components/layouts/MainLayout';
import DashboardLayout from '../components/layouts/DashboardLayout';

// Auth
import ProtectedRoute from '../components/auth/ProtectedRoute';

// Feature flags
import { isFeatureEnabled, FeatureFlags } from '../utils/featureFlags';

// Loading component
const LoadingFallback = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
  </div>
);

// Lazy-loaded pages
const Landing = lazy(() => import('../pages/Landing'));
const Login = lazy(() => import('../pages/Login'));
const Register = lazy(() => import('../pages/Register'));
const Dashboard = lazy(() => import('../pages/Dashboard'));
const CreatePod = lazy(() => import('../pages/CreatePod'));
const PodDetails = lazy(() => import('../pages/PodDetails'));
const Services = lazy(() => import('../pages/Services'));
const CreateService = lazy(() => import('../pages/CreateService'));
const CreateServiceByType = lazy(() => import('../pages/CreateServiceByType'));
const ServiceDetails = lazy(() => import('../pages/ServiceDetails'));
const VMs = lazy(() => import('../pages/VMs'));
const CreateVM = lazy(() => import('../pages/CreateVM'));
const VMDetails = lazy(() => import('../pages/VMDetails'));
const Billing = lazy(() => import('../pages/Billing'));
const Settings = lazy(() => import('../pages/Settings'));
const Support = lazy(() => import('../pages/Support'));
const NotFound = lazy(() => import('../pages/NotFound'));

const AppRoutes = () => {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <Routes>
        {/* Public routes */}
        <Route element={<MainLayout />}>
          <Route path="/" element={
            <Suspense fallback={<LoadingFallback />}>
              <Landing />
            </Suspense>
          } />
          <Route path="/login" element={
            <ProtectedRoute requireAuth={false}>
              <Suspense fallback={<LoadingFallback />}>
                <Login />
              </Suspense>
            </ProtectedRoute>
          } />
          <Route path="/register" element={
            <ProtectedRoute requireAuth={false}>
              <Suspense fallback={<LoadingFallback />}>
                <Register />
              </Suspense>
            </ProtectedRoute>
          } />
        </Route>

        {/* Protected routes */}
        <Route element={
          <ProtectedRoute>
            <DashboardLayout />
          </ProtectedRoute>
        }>
          {/* Default dashboard route - redirects based on feature flags */}
          <Route path="/dashboard" element={
            isFeatureEnabled(FeatureFlags.PODS_FEATURE)
              ? <Suspense fallback={<LoadingFallback />}><Dashboard /></Suspense>
              : <Navigate to="/dashboard/services" replace />
          } />

          {/* Pod routes - only available if PODS_FEATURE is enabled */}
          {isFeatureEnabled(FeatureFlags.PODS_FEATURE) && (
            <>
              <Route path="/dashboard/pods/:id" element={
                <Suspense fallback={<LoadingFallback />}>
                  <PodDetails />
                </Suspense>
              } />
              <Route path="/dashboard/pods/create" element={
                <Suspense fallback={<LoadingFallback />}>
                  <CreatePod />
                </Suspense>
              } />
            </>
          )}

          {/* Services routes - always available */}
          <Route path="/dashboard/services" element={
            <Suspense fallback={<LoadingFallback />}>
              <Services />
            </Suspense>
          } />
          <Route path="/dashboard/services/create" element={
            <Suspense fallback={<LoadingFallback />}>
              <CreateService />
            </Suspense>
          } />
          <Route path="/dashboard/services/create/:type" element={
            <Suspense fallback={<LoadingFallback />}>
              <CreateServiceByType />
            </Suspense>
          } />
          <Route path="/dashboard/services/:id" element={
            <Suspense fallback={<LoadingFallback />}>
              <ServiceDetails />
            </Suspense>
          } />

          {/* VM routes - only available if VM_FEATURE is enabled */}
          {isFeatureEnabled(FeatureFlags.VM_FEATURE) && (
            <>
              <Route path="/dashboard/vms" element={
                <Suspense fallback={<LoadingFallback />}>
                  <VMs />
                </Suspense>
              } />
              <Route path="/dashboard/vms/create" element={
                <Suspense fallback={<LoadingFallback />}>
                  <CreateVM />
                </Suspense>
              } />
              <Route path="/dashboard/vms/:id" element={
                <Suspense fallback={<LoadingFallback />}>
                  <VMDetails />
                </Suspense>
              } />
            </>
          )}
          <Route path="/dashboard/billing" element={
            <Suspense fallback={<LoadingFallback />}>
              <Billing />
            </Suspense>
          } />
          <Route path="/dashboard/settings" element={
            <Suspense fallback={<LoadingFallback />}>
              <Settings />
            </Suspense>
          } />
          <Route path="/dashboard/support" element={
            <Suspense fallback={<LoadingFallback />}>
              <Support />
            </Suspense>
          } />
        </Route>

        {/* 404 route */}
        <Route path="*" element={
          <Suspense fallback={<LoadingFallback />}>
            <NotFound />
          </Suspense>
        } />
      </Routes>
    </Suspense>
  );
};

export default AppRoutes;