import { useState, useEffect, useRef } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { useServices } from '../hooks/useServices';
import Button from '../components/common/Button';
import { supabase } from '../lib/supabase';
import {
  ArrowLeft,
  ExternalLink,
  RefreshCw,
  Play,
  Square,
  FileText,
  Terminal,
  Settings,
  Database,
  Clock,
  Loader,
  Server,
  Link2,
  Trash2,
  Key
} from 'lucide-react';
import DeleteServiceModal from '../components/modals/DeleteServiceModal';

// Import service-specific components
import WahaServiceDetails from '../components/services/WahaServiceDetails';
import WarungServiceDetails from '../components/services/WarungServiceDetails';
import PesanServiceDetails from '../components/services/PesanServiceDetails';
import BeetleHRServiceDetails from '../components/services/BeetleHRServiceDetails';
import DefaultServiceDetails from '../components/services/DefaultServiceDetails';

const ServiceDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { service, loading, error, refreshServices } = useServices(id);
  const navigate = useNavigate();

  // Tab state
  const [activeTab, setActiveTab] = useState<'access' | 'logs' | 'config'>('access');

  const [isRestarting, setIsRestarting] = useState(false);
  const [isStopping, setIsStopping] = useState(false);
  const [isStarting, setIsStarting] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [previousStatus, setPreviousStatus] = useState<string | null>(null);
  const pollingIntervalRef = useRef<number | null>(null);

  // Delete modal state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  // Effect to start polling when service is in provisioning state
  useEffect(() => {
    if (service && service.status === 'provisioning') {
      // Set previous status on first load
      if (previousStatus === null) {
        setPreviousStatus(service.status);
      }

      // Start polling every 1 second
      const startPolling = () => {
        // Clear any existing interval
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
        }

        // Set new interval
        pollingIntervalRef.current = window.setInterval(checkServiceStatus, 1000);
      };

      startPolling();

      // Cleanup function to clear interval when component unmounts or status changes
      return () => {
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }
      };
    } else if (pollingIntervalRef.current) {
      // If service is not in provisioning state but we have an interval, clear it
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
  }, [service, previousStatus, id]);

  // Function to check service status
  const checkServiceStatus = async () => {
    if (!id) return;

    try {
      const { data, error } = await supabase
        .from('services')
        .select('status')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error checking service status:', error);
        return;
      }

      // If status has changed from provisioning to something else, refresh the page
      if (data && previousStatus === 'provisioning' && data.status !== 'provisioning') {
        window.location.reload();
      }

      // Update previous status
      if (data && data.status !== previousStatus) {
        setPreviousStatus(data.status);
      }
    } catch (err) {
      console.error('Error in status check:', err);
    }
  };



  // Format price in Indonesian Rupiah with currency symbol
  const formatPrice = (price: number) => {
    return 'Rp ' + new Intl.NumberFormat('id-ID', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  // We're now using the direct service properties for URLs

  // Mock handlers for service actions
  const handleStart = () => {
    setIsStarting(true);
    setTimeout(() => {
      setIsStarting(false);
      // In a real app, this would update the service status via API
    }, 1500);
  };

  const handleStop = () => {
    setIsStopping(true);
    setTimeout(() => {
      setIsStopping(false);
      // In a real app, this would update the service status via API
    }, 1500);
  };

  const handleRestart = () => {
    setIsRestarting(true);
    setTimeout(() => {
      setIsRestarting(false);
      // In a real app, this would update the service status via API
    }, 2000);
  };

  const handleRefresh = () => {
    setIsRefreshing(true);
    // Use the refreshServices function to fetch the latest data
    refreshServices();
    // Reset the refreshing state after a short delay to show the loading indicator
    setTimeout(() => {
      setIsRefreshing(false);
    }, 500);
  };


  if (loading) {
    return (
      <div className="space-y-6">
        {/* Skeleton for header */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="flex items-center mb-4 md:mb-0">
              <div className="mr-4">
                <div className="h-9 w-20 bg-gray-200 rounded animate-pulse"></div>
              </div>
              <div>
                <div className="h-8 w-48 bg-gray-200 rounded animate-pulse mb-2"></div>
                <div className="h-4 w-32 bg-gray-200 rounded animate-pulse"></div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-9 w-24 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-9 w-24 bg-gray-200 rounded animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* Skeleton for service details */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="h-6 w-32 bg-gray-200 rounded animate-pulse mb-4"></div>
          <div className="space-y-4">
            <div className="h-24 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-24 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-24 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-500 mb-4">Error loading service details</div>
          <Button variant="secondary" onClick={() => navigate('/dashboard/services')}>
            Back to Services
          </Button>
        </div>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-gray-500 mb-4">Service not found</div>
          <Button variant="secondary" onClick={() => navigate('/dashboard/services')}>
            Back to Services
          </Button>
        </div>
      </div>
    );
  }

  // Render different content based on the service template's slug_id
  const renderServiceSpecificContent = () => {
    const slugId = service.service_templates?.slug_id;

    switch (slugId) {
      case 'waha':
        return <WahaServiceDetails service={service} />;

      case 'warung':
        return <WarungServiceDetails service={service} />;

      case 'beetlehr':
        return <BeetleHRServiceDetails service={service} />;

      case 'pesan':
        return <PesanServiceDetails service={service} />;

      default:
        // Use the default service details component when no specific component is found
        return <DefaultServiceDetails service={service} />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Enhanced header with back button, service name, and key details */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div className="flex items-center mb-4 md:mb-0">
            <Link to="/dashboard/services" className="mr-4">
              <Button variant="secondary" size="sm">
                <ArrowLeft size={16} className="mr-2" />
                Back
              </Button>
            </Link>
            <div>
              <div className="flex items-center">
                <h1 className="text-2xl font-bold text-gray-900">{service.name}</h1>
                <span className={`ml-3 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                  service.status === 'active'
                    ? 'bg-green-100 text-green-800'
                    : service.status === 'suspended'
                    ? 'bg-yellow-100 text-yellow-800'
                    : service.status === 'stopped'
                    ? 'bg-red-100 text-red-800'
                    : service.status === 'provisioning'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {service.status}
                </span>
              </div>
              <p className="mt-1 text-sm text-gray-500">
                {service.service_templates?.name || 'Service'} • {service.plan} • {formatPrice(service.price)}
              </p>
            </div>
          </div>

          {/* Quick action buttons in header */}
          {service.status !== 'provisioning' && (
            <div className="flex items-center space-x-2">
              <Button
                variant="danger"
                size="sm"
                onClick={() => setIsDeleteModalOpen(true)}
              >
                <Trash2 size={16} className="mr-2" />
                Delete
              </Button>

              {service.status === 'active' ? (
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={handleStop}
                  isLoading={isStopping}
                  disabled={isStopping || isRestarting || loading}
                >
                  <Square size={16} className="mr-2" />
                  {isStopping ? 'Stopping...' : 'Stop'}
                </Button>
              ) : (
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleStart}
                  isLoading={isStarting}
                  disabled={isStarting || isRestarting || loading}
                >
                  <Play size={16} className="mr-2" />
                  {isStarting ? 'Starting...' : 'Start'}
                </Button>
              )}
              <Button
                variant="secondary"
                size="sm"
                onClick={handleRestart}
                isLoading={isRestarting}
                disabled={isRestarting || isStarting || isStopping || loading}
              >
                <RefreshCw size={16} className={`mr-2 ${isRestarting ? 'animate-spin' : ''}`} />
                {isRestarting ? 'Restarting...' : 'Restart'}
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Service Status - Only show when provisioning */}
      {service.status === 'provisioning' && (
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex flex-col items-center justify-center py-6">
            <div className="mb-6">
              <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center">
                <Loader className="h-8 w-8 text-blue-600 animate-spin" />
              </div>
            </div>

            <h3 className="text-lg font-medium text-gray-900 mb-2">Provisioning Your Service</h3>
            <p className="text-sm text-gray-500 mb-6 text-center max-w-md">
              Preparing your {service.service_templates?.name || 'service'}. This may take a few minutes.
              Your service will be ready to use soon.
            </p>

            <div className="flex space-x-3">
              <Button
                variant="primary"
                size="sm"
                onClick={handleRefresh}
                isLoading={isRefreshing || loading}
                disabled={isRefreshing || loading}
              >
                <RefreshCw size={16} className={`mr-2 ${isRefreshing || loading ? 'animate-spin' : ''}`} />
                {isRefreshing || loading ? 'Refreshing...' : 'Refresh Status'}
              </Button>
              <Button
                as={Link}
                to="/dashboard/services"
                variant="secondary"
                size="sm"
              >
                View All Services
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Service-specific content based on template slug_id */}
      {service.status !== 'provisioning' && renderServiceSpecificContent()}

      {/* Service Details Card - Only show when not provisioning */}
      {service.status !== 'provisioning' && (
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex items-center">
              <div className="mr-4">
                <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                  service.status === 'active'
                    ? 'bg-green-100'
                    : service.status === 'suspended'
                    ? 'bg-yellow-100'
                    : service.status === 'stopped'
                    ? 'bg-red-100'
                    : 'bg-gray-100'
                }`}>
                  <Server className={`h-6 w-6 ${
                    service.status === 'active'
                      ? 'text-green-600'
                      : service.status === 'suspended'
                      ? 'text-yellow-600'
                      : service.status === 'stopped'
                      ? 'text-red-600'
                      : 'text-gray-600'
                  }`} />
                </div>
              </div>
              <div>
                <div className="text-sm font-medium text-gray-500">Status</div>
                <div className="font-medium text-gray-900 capitalize">{service.status}</div>
                <div className="text-sm text-gray-500">
                  <Clock size={14} className="inline mr-1" />
                  <span className="whitespace-pre-line">Expires: {service.expiryFormatted}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      {service.status !== 'provisioning' && (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
          <div className="border-b border-gray-200">
            <div className="px-4 sm:px-6">
              <nav className="flex flex-wrap -mb-px space-x-1 sm:space-x-4">
                <button
                  onClick={() => setActiveTab('access')}
                  className={`py-4 px-3 sm:px-4 text-sm font-medium border-b-2 transition-colors duration-200 ease-in-out flex items-center whitespace-nowrap ${
                    activeTab === 'access'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                  aria-current={activeTab === 'access' ? 'page' : undefined}
                >
                  <Link2 size={16} className="mr-2 flex-shrink-0" />
                  <span>Access</span>
                </button>
                {/* Logs tab commented out for now
                <button
                  onClick={() => setActiveTab('logs')}
                  className={`py-4 px-3 sm:px-4 text-sm font-medium border-b-2 transition-colors duration-200 ease-in-out flex items-center whitespace-nowrap ${
                    activeTab === 'logs'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                  aria-current={activeTab === 'logs' ? 'page' : undefined}
                >
                  <Terminal size={16} className="mr-2 flex-shrink-0" />
                  <span>Logs</span>
                </button>
                */}
                <button
                  onClick={() => setActiveTab('config')}
                  className={`py-4 px-3 sm:px-4 text-sm font-medium border-b-2 transition-colors duration-200 ease-in-out flex items-center whitespace-nowrap ${
                    activeTab === 'config'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                  aria-current={activeTab === 'config' ? 'page' : undefined}
                >
                  <Settings size={16} className="mr-2 flex-shrink-0" />
                  <span>Configuration</span>
                </button>
              </nav>
            </div>
          </div>

          <div className="p-6">
            {/* Access Tab */}
            {activeTab === 'access' && (
              <div className="space-y-6">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                  <div>
                    <h2 className="text-lg font-medium text-gray-900">Access</h2>
                    <p className="text-sm text-gray-500 mt-1">Connect to your service using these endpoints</p>
                  </div>
                </div>

                {/* Access points grid */}
                {(service.public_url || service.admin_url || service.api_url || service.database_url) ? (
                  <div className="grid grid-cols-1 gap-4">
                    {/* Public URL */}
                    {service.public_url && (
                      <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3 bg-gray-50">
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                              <ExternalLink size={16} className="text-blue-600" />
                            </div>
                            <div className="font-medium">Public URL</div>
                          </div>
                          <div className="flex space-x-2">
                            <button
                              className="text-gray-400 hover:text-gray-500"
                              title="Copy URL"
                              onClick={() => navigator.clipboard.writeText(service.public_url || '')}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-mono bg-gray-50 p-2 rounded border border-gray-200 text-gray-600 flex-1 truncate">
                              {service.public_url}
                            </div>
                            <Button
                              as="a"
                              href={service.public_url}
                              target="_blank"
                              variant="primary"
                              size="sm"
                              className="ml-3 flex-shrink-0"
                            >
                              Open
                            </Button>
                          </div>
                          <p className="mt-3 text-sm text-gray-500">
                            The main URL for accessing your service. This is what your users will see.
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Admin Console */}
                    {service.admin_url && (
                      <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3 bg-gray-50">
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                              <Settings size={16} className="text-purple-600" />
                            </div>
                            <div className="font-medium">Admin Console</div>
                          </div>
                          <div className="flex space-x-2">
                            <button
                              className="text-gray-400 hover:text-gray-500"
                              title="Copy URL"
                              onClick={() => navigator.clipboard.writeText(service.admin_url || '')}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-mono bg-gray-50 p-2 rounded border border-gray-200 text-gray-600 flex-1 truncate">
                              {service.admin_url}
                            </div>
                            <Button
                              as="a"
                              href={service.admin_url}
                              target="_blank"
                              variant="primary"
                              size="sm"
                              className="ml-3 flex-shrink-0"
                            >
                              Open
                            </Button>
                          </div>
                          <p className="mt-3 text-sm text-gray-500">
                            Administrative interface for managing your service settings and data.
                          </p>
                        </div>
                      </div>
                    )}

                    {/* API Documentation */}
                    {service.api_url && (
                      <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3 bg-gray-50">
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                              <FileText size={16} className="text-green-600" />
                            </div>
                            <div className="font-medium">API Documentation</div>
                          </div>
                          <div className="flex space-x-2">
                            <button
                              className="text-gray-400 hover:text-gray-500"
                              title="Copy URL"
                              onClick={() => navigator.clipboard.writeText(service.api_url || '')}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-mono bg-gray-50 p-2 rounded border border-gray-200 text-gray-600 flex-1 truncate">
                              {service.api_url}
                            </div>
                            <Button
                              as="a"
                              href={service.api_url}
                              target="_blank"
                              variant="primary"
                              size="sm"
                              className="ml-3 flex-shrink-0"
                            >
                              Open
                            </Button>
                          </div>
                          <p className="mt-3 text-sm text-gray-500">
                            Documentation for the API endpoints available for your service.
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Database Console */}
                    {service.database_url && (
                      <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3 bg-gray-50">
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
                              <Database size={16} className="text-yellow-600" />
                            </div>
                            <div className="font-medium">Database Console</div>
                          </div>
                          <div className="flex space-x-2">
                            <button
                              className="text-gray-400 hover:text-gray-500"
                              title="Copy URL"
                              onClick={() => navigator.clipboard.writeText(service.database_url || '')}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-mono bg-gray-50 p-2 rounded border border-gray-200 text-gray-600 flex-1 truncate">
                              {service.database_url}
                            </div>
                            <Button
                              as="a"
                              href={service.database_url}
                              target="_blank"
                              variant="primary"
                              size="sm"
                              className="ml-3 flex-shrink-0"
                            >
                              Open
                            </Button>
                          </div>
                          <p className="mt-3 text-sm text-gray-500">
                            Direct access to your service's database for advanced management.
                          </p>
                        </div>
                      </div>
                    )}

                    {/* API Key */}
                    {service.api_key && (
                      <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3 bg-gray-50">
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center mr-3">
                              <Key size={16} className="text-indigo-600" />
                            </div>
                            <div className="font-medium">API Key</div>
                          </div>
                          <div className="flex space-x-2">
                            <button
                              className="text-gray-400 hover:text-gray-500"
                              title="Copy API Key"
                              onClick={() => navigator.clipboard.writeText(service.api_key || '')}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-mono bg-gray-50 p-2 rounded border border-gray-200 text-gray-600 flex-1 truncate">
                              {service.api_key}
                            </div>
                          </div>
                          <p className="mt-3 text-sm text-gray-500">
                            Use this API key to authenticate your requests to the service API.
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Username */}
                    {service.admin_username && (
                      <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3 bg-gray-50">
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                              </svg>
                            </div>
                            <div className="font-medium">Username</div>
                          </div>
                          <div className="flex space-x-2">
                            <button
                              className="text-gray-400 hover:text-gray-500"
                              title="Copy Username"
                              onClick={() => navigator.clipboard.writeText(service.admin_username || '')}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-mono bg-gray-50 p-2 rounded border border-gray-200 text-gray-600 flex-1 truncate">
                              {service.admin_username}
                            </div>
                          </div>
                          <p className="mt-3 text-sm text-gray-500">
                            Username for accessing the admin panel or service dashboard.
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Password */}
                    {service.admin_password && (
                      <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3 bg-gray-50">
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center mr-3">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                              </svg>
                            </div>
                            <div className="font-medium">Password</div>
                          </div>
                          <div className="flex space-x-2">
                            <button
                              className="text-gray-400 hover:text-gray-500"
                              title="Copy Password"
                              onClick={() => navigator.clipboard.writeText(service.admin_password || '')}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-mono bg-gray-50 p-2 rounded border border-gray-200 text-gray-600 flex-1 truncate">
                              {service.admin_password}
                            </div>
                          </div>
                          <p className="mt-3 text-sm text-gray-500">
                            Password for accessing the admin panel or service dashboard.
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-12 bg-gray-50 rounded-lg border border-gray-200">
                    <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-4">
                      <Link2 size={20} className="text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-1">No access points available</h3>
                    <p className="text-sm text-gray-500 max-w-md mx-auto">
                      Access points will be available once your service is fully provisioned and active.
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* Logs Tab - Commented out for now
            {activeTab === 'logs' && (
              <div className="space-y-4">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                  <div>
                    <h2 className="text-lg font-medium text-gray-900">Service Logs</h2>
                    <p className="text-sm text-gray-500 mt-1">View real-time logs from your service</p>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <input
                        type="text"
                        className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="Filter logs..."
                      />
                    </div>
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={handleRefresh}
                      isLoading={isRefreshing}
                    >
                      <RefreshCw size={16} className="mr-2" />
                      Refresh Logs
                    </Button>
                  </div>
                </div>

                <div className="flex space-x-2 text-sm">
                  <button className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full font-medium">All Logs</button>
                  <button className="px-3 py-1 text-gray-500 hover:bg-gray-100 rounded-full">Errors</button>
                  <button className="px-3 py-1 text-gray-500 hover:bg-gray-100 rounded-full">Warnings</button>
                  <button className="px-3 py-1 text-gray-500 hover:bg-gray-100 rounded-full">Info</button>
                </div>

                <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-auto max-h-[400px] shadow-inner">
                  <div className="flex justify-between items-center mb-3 text-xs text-gray-400 border-b border-gray-700 pb-2">
                    <span>Showing log entries</span>
                    <button className="hover:text-white">
                      Download Logs
                    </button>
                  </div>

                  <div className="text-center py-8 text-gray-400">
                    <Terminal className="h-10 w-10 mx-auto mb-3 opacity-20" />
                    <p>No logs available for this service.</p>
                    <p className="text-xs mt-1">Logs will appear here when your service is active.</p>
                  </div>
                </div>

                <div className="flex justify-between items-center text-sm text-gray-500">
                  <span>Auto-refresh: Off</span>
                  <button className="text-blue-500 hover:text-blue-600">
                    Clear Logs
                  </button>
                </div>
              </div>
            )}
            */}

            {/* Configuration Tab */}
            {activeTab === 'config' && (
              <div className="space-y-6">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                  <div>
                    <h2 className="text-lg font-medium text-gray-900">Configuration</h2>
                    <p className="text-sm text-gray-500 mt-1">View and manage your service settings</p>
                  </div>
                </div>

                {/* Service Details Section */}
                <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                    <h3 className="text-md font-medium text-gray-900">Service Details</h3>
                  </div>
                  <div className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-3 bg-gray-50 rounded-lg border border-gray-100">
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Service Type</div>
                        <div className="mt-1 font-medium text-gray-900">{service.service_templates?.name || 'Service'}</div>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg border border-gray-100">
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</div>
                        <div className="mt-1 font-medium text-gray-900">{service.plan}</div>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg border border-gray-100">
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Created</div>
                        <div className="mt-1 font-medium text-gray-900">{new Date(service.created_at).toLocaleDateString()}</div>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg border border-gray-100">
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Price</div>
                        <div className="mt-1 font-medium text-gray-900">{formatPrice(service.price)}</div>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg border border-gray-100">
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Status</div>
                        <div className="mt-1 font-medium text-gray-900 capitalize">{service.status}</div>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg border border-gray-100">
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Expires</div>
                        <div className="mt-1 font-medium text-gray-900 whitespace-pre-line">{service.expiryFormatted}</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Resource Allocation Section - Commented out for now
                <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                    <h3 className="text-md font-medium text-gray-900">Resource Allocation</h3>
                  </div>
                  <div className="p-4">
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium text-gray-700">CPU Usage</span>
                          <span className="text-sm font-medium text-gray-700">25%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div className="bg-blue-600 h-2.5 rounded-full" style={{ width: '25%' }}></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium text-gray-700">Memory Usage</span>
                          <span className="text-sm font-medium text-gray-700">512MB / 2GB</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div className="bg-green-600 h-2.5 rounded-full" style={{ width: '25%' }}></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium text-gray-700">Storage Usage</span>
                          <span className="text-sm font-medium text-gray-700">2.5GB / 10GB</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div className="bg-purple-600 h-2.5 rounded-full" style={{ width: '25%' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                */}

                {/* Environment Variables Section - Commented out for now
                <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-200 flex justify-between items-center">
                    <h3 className="text-md font-medium text-gray-900">Environment Variables</h3>
                    <Button
                      variant="secondary"
                      size="sm"
                    >
                      Edit Variables
                    </Button>
                  </div>
                  <div className="p-4">
                    <div className="bg-gray-50 p-3 rounded-lg border border-gray-200 font-mono text-sm">
                      <div className="flex border-b border-gray-200 pb-2 mb-2">
                        <div className="w-1/3 font-medium text-gray-700">Key</div>
                        <div className="w-2/3 font-medium text-gray-700">Value</div>
                      </div>
                      <div className="flex py-2">
                        <div className="w-1/3 text-gray-900">DATABASE_URL</div>
                        <div className="w-2/3 text-gray-500">****************************************/mydb</div>
                      </div>
                      <div className="flex py-2 border-t border-gray-200">
                        <div className="w-1/3 text-gray-900">API_KEY</div>
                        <div className="w-2/3 text-gray-500">••••••••••••••••</div>
                      </div>
                      <div className="flex py-2 border-t border-gray-200">
                        <div className="w-1/3 text-gray-900">NODE_ENV</div>
                        <div className="w-2/3 text-gray-500">production</div>
                      </div>
                    </div>
                  </div>
                </div>
                */}

                {/* Advanced Settings Section - Commented out for now
                <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                    <h3 className="text-md font-medium text-gray-900">Advanced Settings</h3>
                  </div>
                  <div className="p-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-100">
                        <div>
                          <div className="font-medium text-gray-900">Auto-scaling</div>
                          <div className="text-sm text-gray-500">Automatically adjust resources based on usage</div>
                        </div>
                        <div className="relative inline-block w-10 mr-2 align-middle select-none">
                          <input type="checkbox" name="toggle" id="auto-scaling" className="sr-only" />
                          <div className="block bg-gray-300 w-10 h-6 rounded-full"></div>
                          <div className="dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition"></div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-100">
                        <div>
                          <div className="font-medium text-gray-900">Automatic Backups</div>
                          <div className="text-sm text-gray-500">Daily backups of your service data</div>
                        </div>
                        <div className="relative inline-block w-10 mr-2 align-middle select-none">
                          <input type="checkbox" name="toggle" id="auto-backup" className="sr-only" checked />
                          <div className="block bg-blue-500 w-10 h-6 rounded-full"></div>
                          <div className="dot absolute left-5 top-1 bg-white w-4 h-4 rounded-full transition"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                */}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Delete Service Modal */}
      <DeleteServiceModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        serviceName={service.name}
        serviceId={service.id}
      />
    </div>
  );
};

export default ServiceDetails;
