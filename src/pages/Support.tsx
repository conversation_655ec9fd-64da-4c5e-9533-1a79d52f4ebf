import React from 'react';
import { MessageSquare, Mail, Phone, ExternalLink } from 'lucide-react';
import Button from '../components/common/Button';

const faqs = [
  {
    question: 'How do I create a new pod?',
    answer: 'To create a new pod, click the "Create Pod" button in the dashboard or pods page. Follow the setup wizard to configure and deploy your pod.'
  },
  {
    question: 'What payment methods do you accept?',
    answer: 'We accept all major credit cards (Visa, MasterCard, American Express) and PayPal. For enterprise customers, we also support wire transfers.'
  },
  {
    question: 'How do I upgrade my plan?',
    answer: 'You can upgrade your plan at any time from the Billing page. Choose your new plan and follow the prompts to complete the upgrade.'
  },
  {
    question: 'What is your uptime guarantee?',
    answer: 'We guarantee 99.9% uptime for all our services. This is backed by our SLA for professional and enterprise plans.'
  },
];

const Support = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Support</h1>
          <p className="mt-1 text-sm text-gray-500">
            Get help with your account and services
          </p>
        </div>
      </div>

      {/* Contact Options */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <MessageSquare className="h-8 w-8 text-blue-600" />
          <h3 className="mt-4 text-lg font-medium text-gray-900">Live Chat</h3>
          <p className="mt-2 text-sm text-gray-500">
            Chat with our support team in real-time
          </p>
          <Button variant="primary" className="mt-4 w-full">
            Start Chat
          </Button>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <Mail className="h-8 w-8 text-blue-600" />
          <h3 className="mt-4 text-lg font-medium text-gray-900">Email Support</h3>
          <p className="mt-2 text-sm text-gray-500">
            Get help via email within 24 hours
          </p>
          <Button
            as="a"
            href="mailto:<EMAIL>"
            variant="primary"
            className="mt-4 w-full"
          >
            Send Email
          </Button>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <Phone className="h-8 w-8 text-blue-600" />
          <h3 className="mt-4 text-lg font-medium text-gray-900">Phone Support</h3>
          <p className="mt-2 text-sm text-gray-500">
            Available Mon-Fri, 9am-5pm WIB
          </p>
          <Button
            as="a"
            href="tel:+62889-8092-5856"
            variant="primary"
            className="mt-4 w-full"
          >
            Call Us
          </Button>
        </div>
      </div>

      {/* Documentation */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        <div className="px-6 py-5 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Documentation</h2>
        </div>
        <div className="px-6 py-5">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <a
              href="#"
              className="group block p-4 border border-gray-200 rounded-lg hover:border-blue-500"
            >
              <div className="flex items-center justify-between">
                <h3 className="text-base font-medium text-gray-900 group-hover:text-blue-600">
                  Getting Started Guide
                </h3>
                <ExternalLink size={16} className="text-gray-400 group-hover:text-blue-500" />
              </div>
              <p className="mt-1 text-sm text-gray-500">
                Learn the basics of using SumoPod
              </p>
            </a>

            <a
              href="#"
              className="group block p-4 border border-gray-200 rounded-lg hover:border-blue-500"
            >
              <div className="flex items-center justify-between">
                <h3 className="text-base font-medium text-gray-900 group-hover:text-blue-600">
                  API Documentation
                </h3>
                <ExternalLink size={16} className="text-gray-400 group-hover:text-blue-500" />
              </div>
              <p className="mt-1 text-sm text-gray-500">
                Detailed API reference and examples
              </p>
            </a>
          </div>
        </div>
      </div>

      {/* Company Information */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        <div className="px-6 py-5 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Company Information</h2>
        </div>
        <div className="px-6 py-5">
          <h3 className="text-base font-medium text-gray-900">KodingWorks - PT Koding Kreasi Indonesia</h3>
          <p className="mt-2 text-sm text-gray-500">
            Email: <EMAIL><br />
            Phone: +62889-8092-5856
          </p>
        </div>
      </div>

      {/* FAQs */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        <div className="px-6 py-5 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Frequently Asked Questions</h2>
        </div>
        <div className="divide-y divide-gray-200">
          {faqs.map((faq, index) => (
            <div key={index} className="px-6 py-5">
              <h3 className="text-base font-medium text-gray-900">{faq.question}</h3>
              <p className="mt-2 text-sm text-gray-500">{faq.answer}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Support;