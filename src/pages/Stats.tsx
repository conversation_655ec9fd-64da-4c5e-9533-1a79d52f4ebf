import React from 'react';

const Stats = () => {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Website Statistics</h1>
          <p className="mt-2 text-gray-600">Real-time analytics and insights</p>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <iframe 
            plausible-embed 
            src="https://belajar-plausible.ptnezp.easypanel.host/sumopod.com?embed=true&theme=light" 
            scrolling="no" 
            frameBorder="0" 
            loading="lazy" 
            style={{
              width: '1px',
              minWidth: '100%',
              height: '1600px'
            }}
            title="Plausible Analytics"
          />
          <div style={{ fontSize: '14px', paddingBottom: '14px' }}>
            Stats powered by{' '}
            <a 
              target="_blank" 
              rel="noopener noreferrer"
              style={{ 
                color: '#4F46E5', 
                textDecoration: 'underline' 
              }} 
              href="https://plausible.io"
            >
              Plausible Analytics
            </a>
          </div>
        </div>
      </div>
      
      <script async src="https://belajar-plausible.ptnezp.easypanel.host/js/embed.host.js"></script>
    </div>
  );
};

export default Stats;
