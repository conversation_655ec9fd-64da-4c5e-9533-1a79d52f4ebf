import { useEffect, useRef } from 'react';

const Stats = () => {
  const embedRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Load the Plausible embed script
    const script = document.createElement('script');
    script.src = 'https://belajar-plausible.ptnezp.easypanel.host/js/embed.host.js';
    script.async = true;

    script.onload = () => {
      // After script loads, create the iframe manually
      if (embedRef.current) {
        const iframe = document.createElement('iframe');
        iframe.setAttribute('plausible-embed', '');
        iframe.src = 'https://belajar-plausible.ptnezp.easypanel.host/sumopod.com?embed=true&theme=light';
        iframe.style.width = '1px';
        iframe.style.minWidth = '100%';
        iframe.style.height = '1600px';
        iframe.style.border = 'none';
        iframe.style.overflow = 'hidden';
        iframe.title = 'Plausible Analytics';

        embedRef.current.appendChild(iframe);
      }
    };

    document.head.appendChild(script);

    return () => {
      // Cleanup script on unmount
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, []);

  return (
    <div className="min-h-screen">
      <div ref={embedRef} />
      <div style={{ fontSize: '14px', paddingBottom: '14px' }}>
        Stats powered by{' '}
        <a
          target="_blank"
          rel="noopener noreferrer"
          style={{
            color: '#4F46E5',
            textDecoration: 'underline'
          }}
          href="https://plausible.io"
        >
          Plausible Analytics
        </a>
      </div>
    </div>
  );
};

export default Stats;
