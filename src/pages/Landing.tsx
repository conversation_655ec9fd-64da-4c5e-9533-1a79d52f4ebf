import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronRight, Check, Box, Cpu, Shield, Zap, Server, Headphones } from 'lucide-react';
import Button from '../components/common/Button';

const Landing = () => {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="relative pt-24 pb-16 md:pt-32 md:pb-24 overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-50 to-white -z-10"></div>
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-blue-600/5 rounded-full blur-3xl -z-10"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl lg:text-7xl font-bold text-gray-900 tracking-tight">
              <span className="text-blue-600"><PERSON><PERSON> Aplikasi</span><br className="hidden sm:inline" />
              dalam 15 Detik!
            </h1>
            <p className="mt-6 max-w-2xl mx-auto text-xl text-gray-600">
              Sumopod adalah layanan cloud untuk container atau Container as a Service. Beli, deploy, dan kelola container serta aplikasi dengan mudah di platform yang aman dan scalable.
            </p>
            <div className="mt-10 flex flex-col sm:flex-row justify-center gap-4">
              <Button as={Link} to="/register" variant="primary" size="lg">
                Mulai Sekarang
              </Button>
              <Button
                as={Link}
                to="/#features"
                variant="outline"
                size="lg"
              >
                Pelajari Lebih Lanjut <ChevronRight size={16} className="ml-1" />
              </Button>
            </div>

            <div className="mt-12 flex justify-center">
              <img
                src="https://images.pexels.com/photos/1181467/pexels-photo-1181467.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"
                alt="Cloud Computing and Container Management"
                className="w-full max-w-4xl rounded-xl shadow-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-16 md:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              Semua yang Anda butuhkan dalam satu platform
            </h2>
            <p className="mt-4 max-w-2xl mx-auto text-xl text-gray-600">
              SumoPod menawarkan solusi komprehensif untuk manajemen container dan aplikasi
            </p>
            <div className="mt-8">
              <Button
                as={Link}
                to="/#pricing"
                variant="outline"
                size="md"
              >
                Lihat Harga
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <div className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mb-5">
                <Box className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Marketplace Container</h3>
              <p className="text-gray-600">
                Jelajahi dan beli dari perpustakaan container yang luas, semua telah diverifikasi dan siap untuk deployment langsung.
              </p>
            </div>

            {/* Feature 2 */}
            <div className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mb-5">
                <Server className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Deployment Satu Klik</h3>
              <p className="text-gray-600">
                Deploy container ke infrastruktur Anda dengan satu klik, menghilangkan proses konfigurasi yang rumit.
              </p>
            </div>

            {/* Feature 3 */}
            <div className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mb-5">
                <Cpu className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Optimasi Resource</h3>
              <p className="text-gray-600">
                Metrik dan monitoring canggih membantu Anda mengoptimalkan alokasi resource dan mengurangi biaya infrastruktur.
              </p>
            </div>

            {/* Feature 4 */}
            <div className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mb-5">
                <Shield className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Keamanan Enterprise</h3>
              <p className="text-gray-600">
                Keamanan tingkat bank dengan enkripsi, kontrol akses, dan fitur compliance untuk melindungi data Anda.
              </p>
            </div>

            {/* Feature 5 */}
            <div className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mb-5">
                <Zap className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Update Otomatis</h3>
              <p className="text-gray-600">
                Jaga container dan aplikasi Anda tetap up to date dengan update versi otomatis dan patch keamanan.
              </p>
            </div>

            {/* Feature 6 */}
            <div className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100">
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mb-5">
                <Headphones className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Support Ahli 24/7</h3>
              <p className="text-gray-600">
                Tim ahli kami tersedia sepanjang waktu untuk membantu Anda dengan masalah teknis atau pertanyaan apapun.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-16 md:py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

          <div className="mt-12 max-w-lg mx-auto">
            <div className="bg-white rounded-xl shadow-lg overflow-hidden border-2 border-blue-500">
              <div className="p-8 text-center">
                <h3 className="text-2xl font-bold text-gray-900">Mulai Hari Ini</h3>
                <div className="mt-4 flex items-baseline justify-center">
                  <span className="text-5xl font-extrabold text-blue-600">GRATIS</span>
                </div>
                <p className="mt-4 text-gray-600">
                  Semua fitur yang Anda butuhkan untuk mengelola container dan aplikasi secara efektif
                </p>
                <div className="mt-8 space-y-3">
                  <Button
                    as={Link}
                    to="/register"
                    variant="primary"
                    size="lg"
                    className="w-full"
                  >
                    Mulai Sekarang
                  </Button>
                  <Button
                    as={Link}
                    to="/#features"
                    variant="outline"
                    size="lg"
                    className="w-full"
                  >
                    Pelajari Lebih Lanjut
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-blue-600 rounded-2xl overflow-hidden shadow-xl">
            <div className="px-6 py-12 md:p-12 text-center md:text-left md:flex md:items-center md:justify-between">
              <div>
                <h2 className="text-2xl md:text-3xl font-bold text-white">
                  Siap untuk mentransformasi manajemen container Anda?
                </h2>
                <p className="mt-4 text-lg text-blue-100 max-w-2xl">
                  Bergabunglah dengan ribuan bisnis yang menggunakan SumoPod untuk menyederhanakan infrastruktur container dan aplikasi mereka.
                </p>
              </div>
              <div className="mt-8 md:mt-0 flex flex-col md:flex-row gap-4">
                <Button as={Link} to="/register" variant="secondary" size="lg" className="md:w-auto font-semibold">
                  Mulai Sekarang
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Landing;