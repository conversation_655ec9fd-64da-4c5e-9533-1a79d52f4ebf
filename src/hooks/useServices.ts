import { useState, useMemo, useEffect } from 'react';
import { supabase } from '../lib/supabase';

export interface Service {
  id: string;
  user_id: string;
  template_id: string;
  name: string;
  status: 'active' | 'suspended' | 'stopped' | 'provisioning';
  plan: 'monthly' | 'quarterly' | 'biannual' | 'yearly';
  price: number;
  base_price: number;
  created_at: string;
  updated_at: string;
  expires_at: string;
  admin_url: string | null;
  api_url: string | null;
  public_url: string | null;
  database_url: string | null;
  admin_username: string | null;
  admin_password: string | null;
  api_key: string | null;
  // Join with service_templates
  service_templates?: {
    name: string;
    type: string;
    description: string;
    slug_id: string;
  };
  // Formatted expiry date (calculated)
  expiryFormatted?: string;
}

export const useServices = (id?: string) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'suspended' | 'stopped' | 'provisioning'>('all');
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [singleService, setSingleService] = useState<Service | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Format expiry date with remaining days (2-line format)
  const formatExpiryWithDays = (dateString: string) => {
    const now = new Date();
    const expiryDate = new Date(dateString);

    // Format the date as DD/MM/YYYY
    const formattedDate = expiryDate.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });

    // If expired
    if (expiryDate < now) {
      return `${formattedDate}\n(Expired)`;
    }

    const diffTime = Math.abs(expiryDate.getTime() - now.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return `${formattedDate}\n(${diffDays} days left)`;
  };

  // Fetch all services
  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true);
        const { data, error: apiError } = await supabase
          .from('services')
          .select(`
            *,
            service_templates:template_id (
              name,
              type,
              description,
              slug_id
            )
          `)
          .order('created_at', { ascending: false });

        if (apiError) {
          throw apiError;
        }

        setServices(data || []);
        setError(null);
      } catch (err: any) {
        console.error('Error fetching services:', err);
        setError(err.message || 'Failed to load services');
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, [refreshTrigger]);

  // Fetch single service if ID is provided
  useEffect(() => {
    if (!id) {
      setSingleService(null);
      return;
    }

    const fetchService = async () => {
      try {
        setLoading(true);
        const { data, error: apiError } = await supabase
          .from('services')
          .select(`
            *,
            service_templates:template_id (
              name,
              type,
              description,
              slug_id
            )
          `)
          .eq('id', id)
          .single();

        if (apiError) {
          throw apiError;
        }

        setSingleService(data);
        setError(null);
      } catch (err: any) {
        console.error(`Error fetching service with id ${id}:`, err);
        setError(err.message || 'Failed to load service details');
      } finally {
        setLoading(false);
      }
    };

    fetchService();
  }, [id, refreshTrigger]);

  // Add formatted expiry date to services
  const servicesWithFormattedExpiry = useMemo(() => {
    return services.map(service => ({
      ...service,
      expiryFormatted: formatExpiryWithDays(service.expires_at)
    }));
  }, [services]);

  const filteredServices = useMemo(() => {
    return servicesWithFormattedExpiry.filter(service => {
      const matchesStatus = statusFilter === 'all' || service.status === statusFilter;
      const matchesSearch = searchQuery === '' ||
        service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (service.service_templates?.type || '').toLowerCase().includes(searchQuery.toLowerCase());

      return matchesStatus && matchesSearch;
    });
  }, [servicesWithFormattedExpiry, statusFilter, searchQuery]);

  // Function to refresh services data
  const refreshServices = () => {
    setLoading(true);
    setRefreshTrigger(prev => prev + 1);
  };

  return {
    services: filteredServices,
    service: singleService ? {
      ...singleService,
      expiryFormatted: formatExpiryWithDays(singleService.expires_at)
    } : null,
    searchQuery,
    setSearchQuery,
    statusFilter,
    setStatusFilter,
    loading,
    error,
    refreshServices,
    totalServices: services.length,
    activeServices: services.filter(service => service.status === 'active').length,
    suspendedServices: services.filter(service => service.status === 'suspended').length,
    stoppedServices: services.filter(service => service.status === 'stopped').length,
    provisioningServices: services.filter(service => service.status === 'provisioning').length
  };
};
