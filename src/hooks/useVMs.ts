import { useState, useMemo, useEffect } from 'react';
import { supabase } from '../lib/supabase';

export interface VM {
  id: string;
  user_id: string;
  template_id: string;
  name: string;
  status: 'running' | 'stopped' | 'provisioning';
  type: string;
  plan: 'monthly' | 'quarterly' | 'biannual' | 'yearly';
  price: number;
  base_price: number;
  created_at: string;
  updated_at: string;
  expires_at: string;
  cpu: number;
  memory: number;
  storage: number;
  region: string;
  os: string;
  ssh_url: string | null;
  web_console_url: string | null;
  public_ip: string | null;
  logs?: string[];
  // Formatted expiry date (calculated)
  expiryFormatted?: string;
}

export const useVMs = (id?: string) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'running' | 'stopped' | 'provisioning'>('all');
  const [vms, setVMs] = useState<VM[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [singleVM, setSingleVM] = useState<VM | null>(null);

  // Format relative time for expiry
  const formatRelativeTime = (dateString: string) => {
    const now = new Date();
    const expiryDate = new Date(dateString);

    // If expired
    if (expiryDate < now) {
      return 'Expired';
    }

    const diffTime = Math.abs(expiryDate.getTime() - now.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 30) {
      return `${diffDays} days`;
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30);
      return `${months} month${months > 1 ? 's' : ''}`;
    } else {
      const years = Math.floor(diffDays / 365);
      const remainingMonths = Math.floor((diffDays % 365) / 30);
      return `${years} year${years > 1 ? 's' : ''}${remainingMonths > 0 ? `, ${remainingMonths} month${remainingMonths > 1 ? 's' : ''}` : ''}`;
    }
  };

  // Mock data for demonstration
  useEffect(() => {
    const mockVMs: VM[] = [
      {
        id: '1',
        user_id: 'user123',
        template_id: 'template1',
        name: 'Web Server',
        status: 'running',
        type: 'Standard',
        plan: 'monthly',
        price: 100000,
        base_price: 100000,
        created_at: '2023-01-15T00:00:00Z',
        updated_at: '2023-01-15T00:00:00Z',
        expires_at: '2023-02-15T00:00:00Z',
        cpu: 2,
        memory: 4,
        storage: 50,
        region: 'us-east-1',
        os: 'Ubuntu 22.04',
        ssh_url: 'ssh://user@************',
        web_console_url: 'https://console.example.com/vm/1',
        public_ip: '************',
        logs: [
          '2023-01-15 00:00:00 [INFO] VM created',
          '2023-01-15 00:01:00 [INFO] VM started',
          '2023-01-15 00:02:00 [INFO] Services initialized'
        ]
      },
      {
        id: '2',
        user_id: 'user123',
        template_id: 'template2',
        name: 'Database Server',
        status: 'stopped',
        type: 'Performance',
        plan: 'quarterly',
        price: 270000,
        base_price: 100000,
        created_at: '2023-01-10T00:00:00Z',
        updated_at: '2023-01-10T00:00:00Z',
        expires_at: '2023-04-10T00:00:00Z',
        cpu: 4,
        memory: 8,
        storage: 100,
        region: 'us-west-2',
        os: 'CentOS 8',
        ssh_url: 'ssh://user@***********',
        web_console_url: 'https://console.example.com/vm/2',
        public_ip: '***********',
        logs: [
          '2023-01-10 00:00:00 [INFO] VM created',
          '2023-01-10 00:01:00 [INFO] VM started',
          '2023-01-15 12:00:00 [INFO] VM stopped'
        ]
      },
      {
        id: '3',
        user_id: 'user123',
        template_id: 'template3',
        name: 'Test Environment',
        status: 'provisioning',
        type: 'Basic',
        plan: 'monthly',
        price: 50000,
        base_price: 50000,
        created_at: '2023-01-20T00:00:00Z',
        updated_at: '2023-01-20T00:00:00Z',
        expires_at: '2023-02-20T00:00:00Z',
        cpu: 1,
        memory: 2,
        storage: 20,
        region: 'ap-southeast-1',
        os: 'Debian 11',
        ssh_url: null,
        web_console_url: null,
        public_ip: null,
        logs: [
          '2023-01-20 00:00:00 [INFO] VM creation initiated',
          '2023-01-20 00:01:00 [INFO] Provisioning resources',
          '2023-01-20 00:02:00 [INFO] Installing operating system'
        ]
      }
    ];

    // Add formatted expiry date
    const vmsWithFormattedExpiry = mockVMs.map(vm => ({
      ...vm,
      expiryFormatted: formatRelativeTime(vm.expires_at)
    }));

    setVMs(vmsWithFormattedExpiry);
    
    if (id) {
      const vm = vmsWithFormattedExpiry.find(vm => vm.id === id) || null;
      setSingleVM(vm);
    }
    
    setLoading(false);
  }, [id]);

  // Filter VMs based on search query and status filter
  const filteredVMs = useMemo(() => {
    return vms.filter(vm => {
      const matchesSearch = vm.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           (vm.type && vm.type.toLowerCase().includes(searchQuery.toLowerCase()));
      
      const matchesStatus = statusFilter === 'all' || vm.status === statusFilter;
      
      return matchesSearch && matchesStatus;
    });
  }, [vms, searchQuery, statusFilter]);

  // Calculate stats
  const totalVMs = vms.length;
  const activeVMs = vms.filter(vm => vm.status === 'running').length;
  const inactiveVMs = vms.filter(vm => vm.status !== 'running').length;

  return {
    vms: filteredVMs,
    vm: singleVM,
    searchQuery,
    setSearchQuery,
    statusFilter,
    setStatusFilter,
    totalVMs,
    activeVMs,
    inactiveVMs,
    loading,
    error
  };
};
