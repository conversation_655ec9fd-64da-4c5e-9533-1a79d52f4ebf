import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';

export interface ServiceTemplate {
  id: string;
  name: string;
  type: string;
  description: string;
  base_price: number;
  available_plans: {
    plans: ('monthly' | 'quarterly' | 'biannual' | 'yearly')[];
  };
  created_at: string;
  updated_at: string;
  is_active: boolean;
  available_quota: number | null;
  cpu_limit: number | null;
  ram_limit: number | null;
}

export interface ServiceTemplateGroup {
  id: string;
  name: string;
  description: string;
  type: string;
  created_at: string;
  is_active: boolean;
}

export const useServiceTemplates = () => {
  const [templates, setTemplates] = useState<ServiceTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('service_templates')
          .select('*')
          .eq('is_active', true)
          .order('name');

        if (error) {
          throw error;
        }

        setTemplates(data || []);
      } catch (err: any) {
        console.error('Error fetching service templates:', err);
        setError(err.message || 'Failed to load service templates');
      } finally {
        setLoading(false);
      }
    };

    fetchTemplates();
  }, []);

  // Filter templates based on search query
  const filteredTemplates = templates.filter(template =>
    searchQuery === '' ||
    template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    template.type.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return {
    templates: filteredTemplates,
    loading,
    error,
    searchQuery,
    setSearchQuery
  };
};

export const useServiceTemplateGroups = () => {
  const [groups, setGroups] = useState<ServiceTemplateGroup[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchGroups = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('service_template_groups')
          .select('*')
          .eq('is_active', true)
          .order('name');

        if (error) {
          throw error;
        }

        setGroups(data || []);
      } catch (err: any) {
        console.error('Error fetching service template groups:', err);
        setError(err.message || 'Failed to load service template groups');
      } finally {
        setLoading(false);
      }
    };

    fetchGroups();
  }, []);

  return {
    groups,
    loading,
    error
  };
};

export const useServiceTemplatesByType = (type: string | null) => {
  const [templates, setTemplates] = useState<ServiceTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!type) {
      setTemplates([]);
      return;
    }

    const fetchTemplatesByType = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('service_templates')
          .select('*')
          .eq('type', type)
          .eq('is_active', true)
          .order('base_price'); // Order by price (cheapest first)

        if (error) {
          throw error;
        }

        setTemplates(data || []);
      } catch (err: any) {
        console.error('Error fetching service templates by type:', err);
        setError(err.message || 'Failed to load service templates');
      } finally {
        setLoading(false);
      }
    };

    fetchTemplatesByType();
  }, [type]);

  return {
    templates,
    loading,
    error
  };
};

// Hook to get minimum price for each service template group
export const useServiceTemplateGroupsWithPricing = () => {
  const [groupsWithPricing, setGroupsWithPricing] = useState<(ServiceTemplateGroup & { minPrice: number })[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchGroupsWithPricing = async () => {
      try {
        setLoading(true);

        // First get all active groups
        const { data: groups, error: groupsError } = await supabase
          .from('service_template_groups')
          .select('*')
          .eq('is_active', true)
          .order('name');

        if (groupsError) {
          throw groupsError;
        }

        // Then get minimum price for each group type
        const groupsWithPrices = await Promise.all(
          (groups || []).map(async (group) => {
            const { data: templates, error: templatesError } = await supabase
              .from('service_templates')
              .select('base_price')
              .eq('type', group.type)
              .eq('is_active', true)
              .order('base_price')
              .limit(1);

            if (templatesError) {
              console.error(`Error fetching templates for ${group.type}:`, templatesError);
              return { ...group, minPrice: 0 };
            }

            const minPrice = templates && templates.length > 0 ? templates[0].base_price : 0;
            return { ...group, minPrice };
          })
        );

        setGroupsWithPricing(groupsWithPrices);
      } catch (err: any) {
        console.error('Error fetching service template groups with pricing:', err);
        setError(err.message || 'Failed to load service template groups');
      } finally {
        setLoading(false);
      }
    };

    fetchGroupsWithPricing();
  }, []);

  return {
    groups: groupsWithPricing,
    loading,
    error
  };
};
