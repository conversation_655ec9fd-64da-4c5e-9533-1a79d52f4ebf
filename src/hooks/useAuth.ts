import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { User, AuthError } from '@supabase/supabase-js';

interface UseAuthReturn {
  isAuthenticated: boolean;
  user: User | null;
  requestOtp: (email: string, isRegistration?: boolean, metadata?: { [key: string]: any }) => Promise<{ success: boolean; error: AuthError | null }>;
  verifyOtp: (email: string, token: string) => Promise<{ success: boolean; error: AuthError | null }>;
  signInWithGoogle: () => Promise<{ success: boolean; error: AuthError | null }>;
  logout: () => Promise<void>;
  loading: boolean;
}

export const useAuth = (): UseAuthReturn => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    // Check active session when the hook is initialized
    const checkSession = async () => {
      try {
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Error checking session:', error);
          setIsAuthenticated(false);
          setUser(null);
        } else if (data?.session) {
          setIsAuthenticated(true);
          setUser(data.session.user);
        } else {
          setIsAuthenticated(false);
          setUser(null);
        }
      } catch (error) {
        console.error('Session check error:', error);
        setIsAuthenticated(false);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    checkSession();

    // Set up auth state change listener
    const { data: authListener } = supabase.auth.onAuthStateChange(
      (event, session) => {
        if (event === 'SIGNED_IN' && session) {
          setIsAuthenticated(true);
          setUser(session.user);
        } else if (event === 'SIGNED_OUT') {
          setIsAuthenticated(false);
          setUser(null);
        }
      }
    );

    // Clean up subscription on unmount
    return () => {
      authListener.subscription.unsubscribe();
    };
  }, []);

  const requestOtp = async (
    email: string,
    isRegistration: boolean = false,
    metadata?: { [key: string]: any }
  ): Promise<{ success: boolean; error: AuthError | null }> => {
    try {
      // Always set shouldCreateUser to true to allow new signups
      // This is a workaround for the "Signups not allowed for otp" error
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          // Always allow creating a new user
          shouldCreateUser: true,
          // If this is a registration, include the user metadata
          data: isRegistration ? metadata : undefined,
        },
      });

      if (error) {
        console.error('OTP request error:', error);
        return { success: false, error };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('OTP request error:', error);
      return { success: false, error: error as AuthError };
    }
  };

  const verifyOtp = async (
    email: string,
    token: string
  ): Promise<{ success: boolean; error: AuthError | null }> => {
    try {
      const { data, error } = await supabase.auth.verifyOtp({
        email,
        token,
        type: 'email',
      });

      if (error) {
        console.error('OTP verification error:', error);
        return { success: false, error };
      }

      // If verification is successful, the user is now authenticated
      if (data?.user) {
        setIsAuthenticated(true);
        setUser(data.user);
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('OTP verification error:', error);
      return { success: false, error: error as AuthError };
    }
  };

  const signInWithGoogle = async (): Promise<{ success: boolean; error: AuthError | null }> => {
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/dashboard`,
        },
      });

      if (error) {
        console.error('Google sign-in error:', error);
        return { success: false, error };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Google sign-in error:', error);
      return { success: false, error: error as AuthError };
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await supabase.auth.signOut();
      setIsAuthenticated(false);
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return {
    isAuthenticated,
    user,
    requestOtp,
    verifyOtp,
    signInWithGoogle,
    logout,
    loading
  };
};