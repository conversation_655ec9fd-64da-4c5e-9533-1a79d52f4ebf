import { useState } from 'react';
import { supabase } from '../lib/supabase';
import { useNavigate } from 'react-router-dom';

interface CreateServiceParams {
  templateId: string;
  name: string;
  plan: 'monthly' | 'quarterly' | 'biannual' | 'yearly';
}

interface CreateServiceResult {
  serviceId: string | null;
  error: string | null;
}

export const useCreateService = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  const createService = async (params: CreateServiceParams): Promise<CreateServiceResult> => {
    setLoading(true);
    setError(null);
    
    try {
      // Call the Supabase function to create a service
      const { data, error: functionError } = await supabase.rpc(
        'api_create_service',
        {
          p_template_id: params.templateId,
          p_name: params.name,
          p_plan: params.plan
        }
      );

      if (functionError) {
        throw new Error(functionError.message || 'Failed to create service');
      }

      // Return the service ID
      return { serviceId: data, error: null };
    } catch (err: any) {
      console.error('Error creating service:', err);
      setError(err.message || 'An unexpected error occurred');
      return { serviceId: null, error: err.message || 'An unexpected error occurred' };
    } finally {
      setLoading(false);
    }
  };

  return {
    createService,
    loading,
    error
  };
};
