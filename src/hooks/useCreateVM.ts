import { useState } from 'react';
import { supabase } from '../lib/supabase';
import { useNavigate } from 'react-router-dom';

interface CreateVMParams {
  templateId: string;
  name: string;
  plan: 'monthly' | 'quarterly' | 'biannual' | 'yearly';
  resources: {
    cpu: number;
    memory: number;
    storage: number;
  };
}

interface CreateVMResult {
  vmId: string | null;
  error: string | null;
}

export const useCreateVM = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  const createVM = async (params: CreateVMParams): Promise<CreateVMResult> => {
    setLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock successful response
      const mockVMId = `vm-${Math.random().toString(36).substring(2, 10)}`;
      
      return { vmId: mockVMId, error: null };
    } catch (err: any) {
      console.error('Error creating VM:', err);
      setError(err.message || 'An unexpected error occurred');
      return { vmId: null, error: err.message || 'An unexpected error occurred' };
    } finally {
      setLoading(false);
    }
  };

  return {
    createVM,
    loading,
    error
  };
};
