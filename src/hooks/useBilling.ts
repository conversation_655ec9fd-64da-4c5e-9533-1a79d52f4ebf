import { useState, useEffect } from 'react';
import { billingService, BillingPlan, UserBalance, Transaction, Payment } from '../services/billingService';
import { useAuth } from '../context/AuthContext';
import { useSearchParams } from 'react-router-dom';

export const useBilling = () => {
  const { isAuthenticated } = useAuth();
  const [searchParams, setSearchParams] = useSearchParams();
  const page = parseInt(searchParams.get('page') || '1');
  const perPage = 10; // Number of transactions per page

  const [billingPlans, setBillingPlans] = useState<BillingPlan[]>([]);
  const [userBalance, setUserBalance] = useState<UserBalance | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [totalTransactions, setTotalTransactions] = useState(0);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState({
    plans: true,
    balance: true,
    transactions: true,
    payments: true
  });
  const [error, setError] = useState<string | null>(null);
  const [purchaseLoading, setPurchaseLoading] = useState(false);
  const [purchaseError, setPurchaseError] = useState<string | null>(null);
  const [purchaseSuccess, setPurchaseSuccess] = useState(false);

  // Fetch billing plans
  useEffect(() => {
    const fetchBillingPlans = async () => {
      try {
        setLoading(prev => ({ ...prev, plans: true }));
        const data = await billingService.getBillingPlans();
        setBillingPlans(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching billing plans:', err);
        setError('Failed to load billing plans');
      } finally {
        setLoading(prev => ({ ...prev, plans: false }));
      }
    };

    fetchBillingPlans();
  }, []);

  // Fetch user balance and transactions when authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      setUserBalance(null);
      setTransactions([]);
      setPayments([]);
      return;
    }

    const fetchUserData = async () => {
      try {
        // Fetch user balance
        setLoading(prev => ({ ...prev, balance: true }));
        const balance = await billingService.getUserBalance();
        setUserBalance(balance);

        // Fetch transactions with pagination
        setLoading(prev => ({ ...prev, transactions: true }));
        const offset = (page - 1) * perPage;
        const { data: transactionsData, count } = await billingService.getTransactions(perPage, offset);

        // Set transactions and total count
        setTransactions(transactionsData);
        setTotalTransactions(count);

        // Fetch payments
        setLoading(prev => ({ ...prev, payments: true }));
        const paymentsData = await billingService.getPayments(10);
        setPayments(paymentsData);

        setError(null);
      } catch (err) {
        console.error('Error fetching user billing data:', err);
        setError('Failed to load billing data');
      } finally {
        setLoading(prev => ({
          ...prev,
          balance: false,
          transactions: false,
          payments: false
        }));
      }
    };

    fetchUserData();
  }, [isAuthenticated, page, perPage]);

  // Function to purchase credits
  const purchaseCredits = async (planId: string) => {
    try {
      setPurchaseLoading(true);
      setPurchaseError(null);
      setPurchaseSuccess(false);

      const { success, error, invoiceUrl } = await billingService.createPayment(planId);

      if (success && invoiceUrl) {
        // Redirect to Xendit payment page
        window.location.href = invoiceUrl;
        return; // The page will redirect, so we don't need to update state
      } else {
        setPurchaseError(error || 'Failed to create payment');
      }
    } catch (err) {
      console.error('Error purchasing credits:', err);
      setPurchaseError('An unexpected error occurred');
    } finally {
      setPurchaseLoading(false);
    }
  };

  // Check for payment success/failure in URL params (after redirect from Xendit)
  useEffect(() => {
    const checkPaymentStatus = async () => {
      const urlParams = new URLSearchParams(window.location.search);
      const paymentSuccess = urlParams.get('payment_success');
      const paymentId = urlParams.get('payment_id');

      if (paymentSuccess && paymentId) {
        // Clear the URL parameters
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);

        // Check the payment status in our database
        const { status, error } = await billingService.checkPaymentStatus(parseInt(paymentId));

        if (status === 'completed') {
          setPurchaseSuccess(true);

          // Refresh user data
          const balance = await billingService.getUserBalance();
          setUserBalance(balance);

          // Refresh transactions with current pagination
          const offset = (page - 1) * perPage;
          const { data: transactionsData, count } = await billingService.getTransactions(perPage, offset);
          setTransactions(transactionsData);
          setTotalTransactions(count);

          const paymentsData = await billingService.getPayments(10);
          setPayments(paymentsData);
        } else if (status === 'pending') {
          // Payment is still processing
          setPurchaseError('Your payment is still being processed. Please check back later.');
        } else {
          // Payment failed
          setPurchaseError(error || 'Payment failed. Please try again.');
        }
      }
    };

    checkPaymentStatus();
  }, []);

  // Function to change page
  const setPage = (newPage: number) => {
    setSearchParams({ page: newPage.toString() });
  };

  // Calculate total pages
  const totalPages = Math.max(1, Math.ceil(totalTransactions / perPage));

  return {
    billingPlans,
    userBalance,
    transactions,
    payments,
    loading,
    error,
    purchaseCredits,
    purchaseLoading,
    purchaseError,
    purchaseSuccess,
    resetPurchaseState: () => {
      setPurchaseError(null);
      setPurchaseSuccess(false);
    },
    // Pagination properties
    pagination: {
      page,
      perPage,
      totalPages,
      totalItems: totalTransactions,
      setPage
    }
  };
};
