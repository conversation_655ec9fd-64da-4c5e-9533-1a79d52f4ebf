import { useState } from 'react';
import { supabase } from '../lib/supabase';

interface TopUpResult {
  success: boolean;
  error?: string;
  paymentUrl?: string;
}

export const useTopUp = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const topUp = async (amount: number, paymentMethod?: string): Promise<TopUpResult> => {
    try {
      setLoading(true);
      setError(null);

      // Get the user's session
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        setError('User not authenticated');
        return { success: false, error: 'User not authenticated' };
      }

      // Call the API endpoint to create a payment
      const response = await fetch('https://belajar-sumopod-n8n.ptnezp.easypanel.host/webhook/topup-credit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          amount
        })
      });

      const data = await response.json();

      if (!response.ok) {
        const errorMessage = data.error || 'Failed to create payment';
        setError(errorMessage);
        return { success: false, error: errorMessage };
      }

      // Return success with the payment URL for redirection
      return {
        success: true,
        paymentUrl: data.payment_url
      };
    } catch (err: any) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  return {
    topUp,
    loading,
    error,
    clearError: () => setError(null)
  };
};
