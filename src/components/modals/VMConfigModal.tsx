import React, { useState } from 'react';
import { X, Server, Cpu, HardDrive, MemoryStick } from 'lucide-react';
import Button from '../common/Button';

interface VMConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (config: VMConfig) => void;
  templateName: string;
  templateType: string;
  basePrice: number;
  availablePlans: string[];
  defaultResources: {
    cpu: number;
    memory: number;
    storage: number;
  };
  status?: {
    success: boolean;
    message: string;
    vmId: string | null;
  } | null;
}

export interface VMConfig {
  name: string;
  plan: 'monthly' | 'quarterly' | 'biannual' | 'yearly';
  resources: {
    cpu: number;
    memory: number;
    storage: number;
  };
}

const VMConfigModal: React.FC<VMConfigModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  templateName,
  templateType,
  basePrice,
  availablePlans,
  defaultResources,
  status
}) => {
  const [name, setName] = useState('');
  // Set default plan to the first available plan
  const [plan, setPlan] = useState<'monthly' | 'quarterly' | 'biannual' | 'yearly'>(
    availablePlans.includes('monthly') ? 'monthly' :
    availablePlans.includes('quarterly') ? 'quarterly' :
    availablePlans.includes('biannual') ? 'biannual' : 'yearly'
  );
  const [resources, setResources] = useState({
    cpu: defaultResources.cpu,
    memory: defaultResources.memory,
    storage: defaultResources.storage
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Format price in Indonesian Rupiah with currency symbol
  const formatPrice = (price: number) => {
    return 'Rp ' + new Intl.NumberFormat('id-ID', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      onSubmit({ name, plan, resources });
      setIsSubmitting(false);
    }, 1000);
  };

  // Calculate prices for different plans
  const prices = {
    monthly: basePrice,
    quarterly: basePrice * 3 * 0.9, // 10% discount
    biannual: basePrice * 6 * 0.85, // 15% discount
    yearly: basePrice * 12 * 0.75 // 25% discount
  };

  // Calculate price adjustments based on resources
  const calculateResourcePrice = () => {
    const baseCpu = defaultResources.cpu;
    const baseMemory = defaultResources.memory;
    const baseStorage = defaultResources.storage;

    const cpuPrice = (resources.cpu - baseCpu) * 10000; // 10,000 per additional CPU
    const memoryPrice = (resources.memory - baseMemory) * 5000; // 5,000 per additional GB of RAM
    const storagePrice = (resources.storage - baseStorage) * 1000; // 1,000 per additional GB of storage

    return cpuPrice + memoryPrice + storagePrice;
  };

  const resourcePriceAdjustment = calculateResourcePrice();
  const adjustedBasePrice = basePrice + resourcePriceAdjustment;

  const adjustedPrices = {
    monthly: adjustedBasePrice,
    quarterly: adjustedBasePrice * 3 * 0.9,
    biannual: adjustedBasePrice * 6 * 0.85,
    yearly: adjustedBasePrice * 12 * 0.75
  };

  if (!isOpen) return null;

  return (
    <div className="fixed z-10 inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onClick={onClose}></div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                <Server className="h-6 w-6 text-blue-600" />
              </div>
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Configure Your VM
                  </h3>
                  <button
                    type="button"
                    className="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none"
                    onClick={onClose}
                  >
                    <span className="sr-only">Close</span>
                    <X className="h-6 w-6" />
                  </button>
                </div>

                {status && (
                  <div className={`mt-2 p-2 rounded ${status.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
                    {status.message}
                  </div>
                )}

                <form onSubmit={handleSubmit} className="mt-4">
                  <div className="mb-4">
                    <label htmlFor="vm-name" className="block text-sm font-medium text-gray-700 mb-2">
                      VM Name
                    </label>
                    <input
                      type="text"
                      id="vm-name"
                      className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      placeholder="Enter a name for your VM"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      required
                    />
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Resources
                    </label>

                    <div className="space-y-4">
                      {/* CPU Slider */}
                      <div>
                        <div className="flex justify-between items-center">
                          <div className="flex items-center">
                            <Cpu size={16} className="text-gray-500 mr-1" />
                            <span className="text-sm text-gray-700">CPU</span>
                          </div>
                          <span className="text-sm font-medium text-gray-900">{resources.cpu} vCPU</span>
                        </div>
                        <input
                          type="range"
                          min="1"
                          max="16"
                          step="1"
                          value={resources.cpu}
                          onChange={(e) => setResources({...resources, cpu: parseInt(e.target.value)})}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer mt-2"
                        />
                      </div>

                      {/* Memory Slider */}
                      <div>
                        <div className="flex justify-between items-center">
                          <div className="flex items-center">
                            <MemoryStick size={16} className="text-gray-500 mr-1" />
                            <span className="text-sm text-gray-700">Memory (RAM)</span>
                          </div>
                          <span className="text-sm font-medium text-gray-900">{resources.memory} GB</span>
                        </div>
                        <input
                          type="range"
                          min="1"
                          max="64"
                          step="1"
                          value={resources.memory}
                          onChange={(e) => setResources({...resources, memory: parseInt(e.target.value)})}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer mt-2"
                        />
                      </div>

                      {/* Storage Slider */}
                      <div>
                        <div className="flex justify-between items-center">
                          <div className="flex items-center">
                            <HardDrive size={16} className="text-gray-500 mr-1" />
                            <span className="text-sm text-gray-700">Storage</span>
                          </div>
                          <span className="text-sm font-medium text-gray-900">{resources.storage} GB</span>
                        </div>
                        <input
                          type="range"
                          min="10"
                          max="1000"
                          step="10"
                          value={resources.storage}
                          onChange={(e) => setResources({...resources, storage: parseInt(e.target.value)})}
                          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer mt-2"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Billing Plan
                    </label>
                    <div className="grid grid-cols-2 gap-3">
                      {availablePlans.includes('monthly') && (
                        <div
                          className={`border rounded-md p-3 cursor-pointer ${
                            plan === 'monthly' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                          }`}
                          onClick={() => setPlan('monthly')}
                        >
                          <div className="font-medium">1 Month</div>
                          <div className="text-sm text-gray-500">{formatPrice(adjustedPrices.monthly)}</div>
                        </div>
                      )}
                      {availablePlans.includes('quarterly') && (
                        <div
                          className={`border rounded-md p-3 cursor-pointer ${
                            plan === 'quarterly' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                          }`}
                          onClick={() => setPlan('quarterly')}
                        >
                          <div className="font-medium">3 Months</div>
                          <div className="text-sm text-gray-500">{formatPrice(adjustedPrices.quarterly)}</div>
                        </div>
                      )}
                      {availablePlans.includes('biannual') && (
                        <div
                          className={`border rounded-md p-3 cursor-pointer ${
                            plan === 'biannual' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                          }`}
                          onClick={() => setPlan('biannual')}
                        >
                          <div className="font-medium">6 Months</div>
                          <div className="text-sm text-gray-500">{formatPrice(adjustedPrices.biannual)}</div>
                        </div>
                      )}
                      {availablePlans.includes('yearly') && (
                        <div
                          className={`border rounded-md p-3 cursor-pointer ${
                            plan === 'yearly' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                          }`}
                          onClick={() => setPlan('yearly')}
                        >
                          <div className="font-medium">12 Months</div>
                          <div className="text-sm text-gray-500">{formatPrice(adjustedPrices.yearly)}</div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                    <Button
                      type="submit"
                      variant="primary"
                      isLoading={isSubmitting}
                    >
                      Create VM
                    </Button>
                    <Button
                      type="button"
                      variant="secondary"
                      className="mr-3"
                      onClick={onClose}
                    >
                      Cancel
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VMConfigModal;
