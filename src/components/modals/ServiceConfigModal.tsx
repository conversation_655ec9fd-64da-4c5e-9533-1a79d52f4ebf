import React, { useState } from 'react';
import { X, Cloud } from 'lucide-react';
import Button from '../common/Button';

interface ServiceConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (config: ServiceConfig) => void;
  templateName: string;
  templateType: string;
  basePrice: number;
  availablePlans: string[];
  status?: {
    success: boolean;
    message: string;
    serviceId: string | null;
  } | null;
}

export interface ServiceConfig {
  name: string;
  plan: 'monthly' | 'quarterly' | 'biannual' | 'yearly';
}

const ServiceConfigModal: React.FC<ServiceConfigModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  templateName,
  templateType,
  basePrice,
  availablePlans,
  status
}) => {
  const [name, setName] = useState('');
  // Set default plan to the first available plan
  const [plan, setPlan] = useState<'monthly' | 'quarterly' | 'biannual' | 'yearly'>(
    availablePlans.includes('monthly') ? 'monthly' :
    availablePlans.includes('quarterly') ? 'quarterly' :
    availablePlans.includes('biannual') ? 'biannual' : 'yearly'
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Format price in Indonesian Rupiah with currency symbol
  const formatPrice = (price: number) => {
    return 'Rp ' + new Intl.NumberFormat('id-ID', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      onSubmit({ name, plan });
      setIsSubmitting(false);
      setName('');
      setPlan('monthly');
    }, 1000);
  };

  if (!isOpen) return null;

  // Calculate prices for different plans
  const prices = {
    monthly: basePrice,
    quarterly: basePrice * 3 * 0.9, // 10% discount
    biannual: basePrice * 6 * 0.85, // 15% discount
    yearly: basePrice * 12 * 0.75 // 25% discount
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75" onClick={onClose}></div>
        </div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                <Cloud className="h-6 w-6 text-blue-600" />
              </div>
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Configure Your Service
                  </h3>
                  <button
                    type="button"
                    className="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none"
                    onClick={onClose}
                  >
                    <X size={20} />
                  </button>
                </div>
                <div className="mt-2">
                  <p className="text-sm text-gray-500">
                    You're about to deploy a {templateName} service. Please configure the details below.
                  </p>
                </div>

                {/* Free service notice */}
                {basePrice === 0 && (
                  <div className="mt-4 p-4 rounded-md bg-amber-50 border border-amber-200">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-amber-800">
                          Layanan Gratis - Pemberitahuan Penting
                        </h3>
                        <div className="mt-2 text-sm text-amber-700">
                          <p>
                            Layanan ini akan dimatikan secara otomatis jika tidak digunakan atau tidak aktif dalam periode tertentu.
                            Untuk layanan yang membutuhkan uptime 24/7, silakan gunakan paket berbayar yang menjamin layanan selalu aktif.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {status && !status.success && (
                  <div className="mt-4 p-3 rounded-md bg-red-50 text-red-800">
                    <p className="text-sm font-medium">{status.message}</p>
                  </div>
                )}

                <form onSubmit={handleSubmit} className="mt-4">
                  <div className="mb-4">
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                      Service Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      placeholder={`My ${templateName}`}
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      required
                    />
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Billing Plan
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      {availablePlans.includes('monthly') && (
                        <div
                          className={`border rounded-md p-3 cursor-pointer ${
                            plan === 'monthly' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                          }`}
                          onClick={() => setPlan('monthly')}
                        >
                          <div className="font-medium">Monthly</div>
                          <div className="text-sm text-gray-500">{formatPrice(prices.monthly)}</div>
                        </div>
                      )}
                      {availablePlans.includes('quarterly') && (
                        <div
                          className={`border rounded-md p-3 cursor-pointer ${
                            plan === 'quarterly' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                          }`}
                          onClick={() => setPlan('quarterly')}
                        >
                          <div className="font-medium">3 Months</div>
                          <div className="text-sm text-gray-500">{formatPrice(prices.quarterly)}</div>
                        </div>
                      )}
                      {availablePlans.includes('biannual') && (
                        <div
                          className={`border rounded-md p-3 cursor-pointer ${
                            plan === 'biannual' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                          }`}
                          onClick={() => setPlan('biannual')}
                        >
                          <div className="font-medium">6 Months</div>
                          <div className="text-sm text-gray-500">{formatPrice(prices.biannual)}</div>
                        </div>
                      )}
                      {availablePlans.includes('yearly') && (
                        <div
                          className={`border rounded-md p-3 cursor-pointer ${
                            plan === 'yearly' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                          }`}
                          onClick={() => setPlan('yearly')}
                        >
                          <div className="font-medium">12 Months</div>
                          <div className="text-sm text-gray-500">{formatPrice(prices.yearly)}</div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                    <Button
                      type="submit"
                      variant="primary"
                      isLoading={isSubmitting}
                    >
                      Deploy Service
                    </Button>
                    <Button
                      type="button"
                      variant="secondary"
                      className="mr-3"
                      onClick={onClose}
                    >
                      Cancel
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceConfigModal;
