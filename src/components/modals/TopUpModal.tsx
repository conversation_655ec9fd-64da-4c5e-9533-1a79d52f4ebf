import React, { useState } from 'react';
import { X, AlertCircle } from 'lucide-react';
import Button from '../common/Button';
import { formatIDR, formatNumber } from '../../utils/formatters';
import { useTopUp } from '../../hooks/useTopUp';

interface TopUpModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const TopUpModal: React.FC<TopUpModalProps> = ({ isOpen, onClose }) => {
  const [amount, setAmount] = useState<number>(0);
  const { topUp, loading, error } = useTopUp();

  if (!isOpen) return null;

  const handleSubmit = async () => {
    const result = await topUp(amount);
    if (result.success && result.paymentUrl) {
      // Redirect to Xendit payment page
      window.location.href = result.paymentUrl;
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onClick={onClose} />

        <div className="relative w-full max-w-md rounded-lg bg-white shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-200 px-6 py-4">
            <h3 className="text-lg font-medium text-gray-900">Top Up Balance</h3>
            <button
              onClick={onClose}
              className="rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
            >
              <X size={20} />
            </button>
          </div>

          {/* Content */}
          <div className="p-6">
            <div className="space-y-4">
              {/* Amount Input */}
              <div>
                <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
                  Amount (Rp)
                </label>
                <input
                  type="number"
                  id="amount"
                  value={amount}
                  onChange={(e) => setAmount(Number(e.target.value))}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                  placeholder="Enter amount"
                  min="10000"
                  step="10000"
                />
              </div>

              {/* Quick Amount Buttons */}
              <div className="grid grid-cols-3 gap-2">
                {[50000, 100000, 200000].map((quickAmount) => (
                  <button
                    key={quickAmount}
                    onClick={() => setAmount(quickAmount)}
                    className="rounded-md border border-gray-300 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                  >
                    {formatIDR(quickAmount, 0)}
                  </button>
                ))}
              </div>

              {/* Payment method selection has been removed */}
            </div>
          </div>

          {/* Error message */}
          {error && (
            <div className="px-6 py-2">
              <div className="flex items-center text-red-600 text-sm">
                <AlertCircle className="h-4 w-4 mr-2" />
                <span>{error}</span>
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="border-t border-gray-200 px-6 py-4">
            <Button
              variant="primary"
              className="w-full"
              onClick={handleSubmit}
              disabled={!amount}
              isLoading={loading}
            >
              Top Up
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopUpModal;