import React, { useState } from 'react';
import { X, HelpCircle } from 'lucide-react';
import Button from '../common/Button';
import { formatIDR } from '../../utils/formatters';

interface PodConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (config: PodConfig) => void;
  templateName: string;
  templateIcon: React.ReactNode;
}

export interface PodConfig {
  name: string;
  resources: {
    cpu: number;
    memory: number;
    storage: number;
  };
}

const PodConfigModal: React.FC<PodConfigModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  templateName,
  templateIcon,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [config, setConfig] = useState<PodConfig>({
    name: '',
    resources: {
      cpu: 0.5,
      memory: 1,
      storage: 5,
    },
  });

  const steps = [
    { id: 'basics', name: 'Basics', icon: <HelpCircle size={20} /> },
    { id: 'resources', name: 'Resources', icon: <HelpCircle size={20} /> },
  ];

  const calculateMonthlyCost = () => {
    const cpuCost = config.resources.cpu * 2; // Rp 2 per CPU core
    const memoryCost = config.resources.memory * 1; // Rp 1 per GB of RAM
    const storageCost = config.resources.storage * 0.1; // Rp 0.1 per GB of storage
    return (cpuCost + memoryCost + storageCost).toFixed(2);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onClick={onClose} />

        <div className="relative w-full max-w-2xl rounded-lg bg-white shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-200 px-6 py-4">
            <div className="flex items-center space-x-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-50">
                {templateIcon}
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">Configure {templateName} Pod</h3>
                <p className="text-sm text-gray-500">Step {currentStep + 1} of {steps.length}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
            >
              <X size={20} />
            </button>
          </div>

          {/* Steps */}
          <div className="px-6 py-4">
            {currentStep === 0 && (
              <div className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                    Pod Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    value={config.name}
                    onChange={(e) => setConfig({ ...config, name: e.target.value })}
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                    placeholder="e.g. my-nginx-pod"
                  />
                </div>
              </div>
            )}

            {currentStep === 1 && (
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    CPU Cores
                  </label>
                  <input
                    type="range"
                    min={0.25}
                    max={6}
                    step={0.25}
                    value={config.resources.cpu}
                    onChange={(e) => setConfig({
                      ...config,
                      resources: {
                        ...config.resources,
                        cpu: parseFloat(e.target.value)
                      }
                    })}
                    className="mt-2 w-full"
                  />
                  <div className="mt-1 flex justify-between text-sm text-gray-500">
                    <span>0.25</span>
                    <span>{config.resources.cpu} cores</span>
                    <span>6</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Memory (GB)
                  </label>
                  <input
                    type="range"
                    min={0.25}
                    max={16}
                    step={0.25}
                    value={config.resources.memory}
                    onChange={(e) => setConfig({
                      ...config,
                      resources: {
                        ...config.resources,
                        memory: parseFloat(e.target.value)
                      }
                    })}
                    className="mt-2 w-full"
                  />
                  <div className="mt-1 flex justify-between text-sm text-gray-500">
                    <span>0.25</span>
                    <span>{config.resources.memory} GB</span>
                    <span>16</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Storage (GB)
                  </label>
                  <input
                    type="range"
                    min={5}
                    max={1000}
                    step={5}
                    value={config.resources.storage}
                    onChange={(e) => setConfig({
                      ...config,
                      resources: {
                        ...config.resources,
                        storage: parseFloat(e.target.value)
                      }
                    })}
                    className="mt-2 w-full"
                  />
                  <div className="mt-1 flex justify-between text-sm text-gray-500">
                    <span>5</span>
                    <span>{config.resources.storage} GB</span>
                    <span>1000</span>
                  </div>
                </div>

                <div className="rounded-lg bg-gray-50 p-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">Monthly Cost</span>
                    <span className="text-lg font-semibold text-gray-900">{formatIDR(parseFloat(calculateMonthlyCost()))}</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between border-t border-gray-200 px-6 py-4">
            <Button
              variant="secondary"
              onClick={() => currentStep > 0 ? setCurrentStep(currentStep - 1) : onClose}
            >
              {currentStep > 0 ? 'Previous' : 'Cancel'}
            </Button>
            <Button
              variant="primary"
              onClick={() => {
                if (currentStep < steps.length - 1) {
                  setCurrentStep(currentStep + 1);
                } else {
                  onSubmit(config);
                }
              }}
            >
              {currentStep < steps.length - 1 ? 'Next' : 'Create Pod'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PodConfigModal;