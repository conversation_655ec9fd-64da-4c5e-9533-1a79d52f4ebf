import React from 'react';
import { CreditCard } from 'lucide-react';
import Button from '../common/Button';

const PaymentMethod = () => {
  return (
    <div className="bg-white shadow-sm rounded-lg border border-gray-200">
      <div className="px-6 py-5 border-b border-gray-200">
        <h2 className="text-lg font-medium text-gray-900">Payment Method</h2>
      </div>
      <div className="px-6 py-5">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CreditCard className="h-8 w-8 text-gray-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-900">Visa ending in 4242</p>
              <p className="text-sm text-gray-500">Expires 12/2024</p>
            </div>
          </div>
          <Button variant="primary">
            <CreditCard size={18} className="mr-2" />
            Update Payment Method
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PaymentMethod;