import React from 'react';
import { Pod } from '../../hooks/usePods';

interface PodMetricsProps {
  pod: Pod;
}

const PodMetrics: React.FC<PodMetricsProps> = ({ pod }) => {
  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200 mt-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Resource Usage</h2>
      <div className="grid grid-cols-2 gap-6">
        <div>
          <div className="text-sm text-gray-500">CPU Usage</div>
          <div className="mt-1 text-2xl font-semibold text-gray-900">{pod.cpu}</div>
        </div>
        <div>
          <div className="text-sm text-gray-500">Memory Usage</div>
          <div className="mt-1 text-2xl font-semibold text-gray-900">{pod.memory}</div>
        </div>
      </div>
    </div>
  );
};

export default PodMetrics;