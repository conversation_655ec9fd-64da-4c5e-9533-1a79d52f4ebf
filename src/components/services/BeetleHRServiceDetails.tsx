import React from 'react';
import { Service } from '../../hooks/useServices';

interface BeetleHRServiceDetailsProps {
  service: Service;
}

const BeetleHRServiceDetails: React.FC<BeetleHRServiceDetailsProps> = ({ service }) => {
  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm mt-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">BeetleHR Service Details</h2>
      <p className="text-gray-700 mb-4">
        Your HR management system is ready. Manage employees, attendance, payroll, and more.
      </p>

      {/* BeetleHR-specific content here - Commented out for standardization
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <h3 className="font-medium text-gray-900 mb-2">Employees</h3>
          <p className="text-gray-700">0 employees registered</p>
        </div>
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <h3 className="font-medium text-gray-900 mb-2">Departments</h3>
          <p className="text-gray-700">0 departments created</p>
        </div>
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <h3 className="font-medium text-gray-900 mb-2">Attendance</h3>
          <p className="text-gray-700">No attendance records</p>
        </div>
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <h3 className="font-medium text-gray-900 mb-2">Payroll</h3>
          <p className="text-gray-700">No payroll processed</p>
        </div>
      </div>
      */}
    </div>
  );
};

export default BeetleHRServiceDetails;
