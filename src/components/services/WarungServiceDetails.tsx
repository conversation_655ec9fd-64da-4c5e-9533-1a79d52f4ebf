import React from 'react';
import { Service } from '../../hooks/useServices';

interface WarungServiceDetailsProps {
  service: Service;
}

const WarungServiceDetails: React.FC<WarungServiceDetailsProps> = ({ service }) => {
  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm mt-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">Warung Service Details</h2>
      <p className="text-gray-700 mb-4">
        Your headless eCommerce platform is ready. Manage your products, orders, and customers through the admin panel.
      </p>

      {/* Warung-specific content here - Commented out for standardization
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <h3 className="font-medium text-gray-900 mb-2">Products</h3>
          <p className="text-gray-700">0 active products</p>
        </div>
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <h3 className="font-medium text-gray-900 mb-2">Orders</h3>
          <p className="text-gray-700">0 orders processed</p>
        </div>
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <h3 className="font-medium text-gray-900 mb-2">Customers</h3>
          <p className="text-gray-700">0 registered customers</p>
        </div>
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <h3 className="font-medium text-gray-900 mb-2">Revenue</h3>
          <p className="text-gray-700">Rp 0</p>
        </div>
      </div>
      */}
    </div>
  );
};

export default WarungServiceDetails;
