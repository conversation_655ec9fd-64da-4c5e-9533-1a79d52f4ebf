import React from 'react';
import { Service } from '../../hooks/useServices';

interface WahaServiceDetailsProps {
  service: Service;
}

const WahaServiceDetails: React.FC<WahaServiceDetailsProps> = ({ service }) => {
  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm mt-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">WAHA Service Details</h2>
      <p className="text-gray-700 mb-4">
        Your WhatsApp API Gateway is ready to use. Connect your applications using the API endpoints in the Access tab.
      </p>
      {/* WAHA-specific content here - Commented out for standardization
      <div className="grid grid-cols-1 gap-4 mt-4">
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <h3 className="font-medium text-gray-900 mb-2">WhatsApp Number</h3>
          <p className="text-gray-700">+62812345678</p>
        </div>
      </div>
      */}
    </div>
  );
};

export default WahaServiceDetails;
