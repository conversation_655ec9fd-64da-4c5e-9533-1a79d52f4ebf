import React from 'react';
import { Service } from '../../hooks/useServices';

interface DefaultServiceDetailsProps {
  service: Service;
}

const DefaultServiceDetails: React.FC<DefaultServiceDetailsProps> = ({ service }) => {
  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm mt-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">{service.service_templates?.name || 'Service'} Details</h2>
      <p className="text-gray-700 mb-4">
        Your service is ready to use. Connect to your service using the access points in the Access tab.
      </p>
    </div>
  );
};

export default DefaultServiceDetails;
