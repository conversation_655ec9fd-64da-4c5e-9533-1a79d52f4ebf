import React from 'react';
import { Service } from '../../hooks/useServices';

interface PesanServiceDetailsProps {
  service: Service;
}

const PesanServiceDetails: React.FC<PesanServiceDetailsProps> = ({ service }) => {
  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm mt-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">Pesan Service Details</h2>
      <p className="text-gray-700 mb-4">
        Your hotel management system is ready. Manage bookings, rooms, and guests through the admin panel.
      </p>

      {/* Pesan-specific content here - Commented out for standardization
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <h3 className="font-medium text-gray-900 mb-2">Properties</h3>
          <p className="text-gray-700">0 properties configured</p>
        </div>
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <h3 className="font-medium text-gray-900 mb-2">Bookings</h3>
          <p className="text-gray-700">0 bookings processed</p>
        </div>
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <h3 className="font-medium text-gray-900 mb-2">Rooms</h3>
          <p className="text-gray-700">0 rooms available</p>
        </div>
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <h3 className="font-medium text-gray-900 mb-2">Guests</h3>
          <p className="text-gray-700">0 registered guests</p>
        </div>
      </div>
      */}
    </div>
  );
};

export default PesanServiceDetails;
