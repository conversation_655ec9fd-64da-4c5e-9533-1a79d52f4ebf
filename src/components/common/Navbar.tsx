import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, Container, LogIn } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import Button from './Button';
import Logo from './Logo';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const { isAuthenticated } = useAuth();
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle hash navigation when component mounts
  useEffect(() => {
    // Check if there's a hash in the URL
    if (location.hash) {
      // Wait a bit for the page to fully load
      setTimeout(() => {
        const id = location.hash.substring(1); // remove the # character
        const element = document.getElementById(id);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
    }
  }, [location.hash]);

  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
  }, [isMenuOpen]);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const navbarClass = isScrolled
    ? 'bg-white shadow-md'
    : 'bg-transparent';

  return (
    <nav className={`fixed w-full z-50 transition-all duration-300 ${navbarClass}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <Link to="/" className="flex-shrink-0">
              <Logo />
            </Link>
            <div className="hidden md:block ml-10">
              <div className="flex items-center space-x-8">
                <Link
                  to="/"
                  className={`text-base font-medium transition-colors hover:text-blue-700 ${
                    location.pathname === '/' ? 'text-blue-700' : 'text-gray-800'
                  }`}
                >
                  Home
                </Link>
                <Link
                  to="/#features"
                  className="text-gray-800 text-base font-medium transition-colors hover:text-blue-700 cursor-pointer"
                >
                  Features
                </Link>
                <Link
                  to="/#pricing"
                  className="text-gray-800 text-base font-medium transition-colors hover:text-blue-700 cursor-pointer"
                >
                  Pricing
                </Link>
              </div>
            </div>
          </div>
          <div className="hidden md:flex items-center space-x-4">
            {isAuthenticated ? (
              <Button
                as={Link}
                to="/dashboard"
                variant="secondary"
              >
                Dashboard
              </Button>
            ) : (
              <Button
                as={Link}
                to="/login"
                variant="secondary"
              >
                <LogIn size={18} className="mr-1" />
                Login
              </Button>
            )}
            <Button as={Link} to="/register" variant="primary">
              Get Started
            </Button>
          </div>
          <div className="md:hidden">
            <button
              onClick={toggleMenu}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-800 hover:text-blue-700 focus:outline-none"
            >
              {isMenuOpen ? (
                <X size={24} aria-hidden="true" />
              ) : (
                <Menu size={24} aria-hidden="true" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <div
        className={`md:hidden transition-all duration-300 ease-in-out ${
          isMenuOpen ? 'opacity-100 h-screen' : 'opacity-0 h-0 invisible'
        } bg-white absolute w-full overflow-hidden`}
      >
        <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
          <Link
            to="/"
            className="block py-2 px-3 text-gray-800 hover:bg-gray-100 rounded-md font-medium"
            onClick={() => setIsMenuOpen(false)}
          >
            Home
          </Link>
          <Link
            to="/#features"
            className="block py-2 px-3 text-gray-800 hover:bg-gray-100 rounded-md font-medium"
            onClick={() => setIsMenuOpen(false)}
          >
            Features
          </Link>
          <Link
            to="/#pricing"
            className="block py-2 px-3 text-gray-800 hover:bg-gray-100 rounded-md font-medium"
            onClick={() => setIsMenuOpen(false)}
          >
            Pricing
          </Link>
          <div className="pt-4 pb-3 border-t border-gray-200">
            {isAuthenticated ? (
              <Link
                to="/dashboard"
                className="block w-full py-2 px-3 bg-blue-600 text-white text-center rounded-md font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Dashboard
              </Link>
            ) : (
              <>
                <Link
                  to="/login"
                  className="block py-2 px-3 text-gray-800 hover:bg-gray-100 rounded-md font-medium"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Login
                </Link>
                <Link
                  to="/register"
                  className="block mt-2 w-full py-2 px-3 bg-blue-600 text-white text-center rounded-md font-medium"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Get Started
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;