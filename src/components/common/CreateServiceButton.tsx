import React from 'react';
import { Cloud } from 'lucide-react';
import Button from './Button';
import { Link } from 'react-router-dom';

interface CreateServiceButtonProps {
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
}

const CreateServiceButton: React.FC<CreateServiceButtonProps> = ({ 
  variant = 'primary',
  size = 'md'
}) => {
  return (
    <Button 
      as={Link} 
      to="/dashboard/services/create"
      variant={variant} 
      size={size}
    >
      <Cloud size={size === 'sm' ? 16 : 18} className="mr-2" />
      Add Service
    </Button>
  );
};

export default CreateServiceButton;
