import React from 'react';
import { Server } from 'lucide-react';
import Button from './Button';
import { Link } from 'react-router-dom';

interface CreateVMButtonProps {
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
}

const CreateVMButton: React.FC<CreateVMButtonProps> = ({ 
  variant = 'primary',
  size = 'md'
}) => {
  return (
    <Button 
      as={Link} 
      to="/dashboard/vms/create"
      variant={variant} 
      size={size}
    >
      <Server size={size === 'sm' ? 16 : 18} className="mr-2" />
      Create VM
    </Button>
  );
};

export default CreateVMButton;
