import React from 'react';
import { NavLink } from 'react-router-dom';
import { X, LayoutDashboard, Settings, CreditCard, HelpCircle, UserCircle, Cloud, Server } from 'lucide-react';
import Logo from '../common/Logo';
import { isFeatureEnabled, FeatureFlags } from '../../utils/featureFlags';

interface SidebarProps {
  isOpen: boolean;
  toggleSidebar: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, toggleSidebar }) => {
  // Define all possible navigation items
  const serviceItem = { name: 'Services', path: '/dashboard/services', icon: <Cloud size={20} /> };
  const podsItem = { name: 'Pods', path: '/dashboard', icon: <LayoutDashboard size={20} /> };
  const vmItem = { name: 'VMs', path: '/dashboard/vms', icon: <Server size={20} /> };
  const billingItem = { name: 'Billing', path: '/dashboard/billing', icon: <CreditCard size={20} /> };
  const settingsItem = { name: 'Settings', path: '/dashboard/settings', icon: <Settings size={20} /> };

  // Start with services and other common items
  let navItems = [serviceItem, billingItem, settingsItem];

  // Add Pods item if the feature flag is enabled
  if (isFeatureEnabled(FeatureFlags.PODS_FEATURE)) {
    navItems = [podsItem, ...navItems];
  }

  // Add VM item if the feature flag is enabled
  if (isFeatureEnabled(FeatureFlags.VM_FEATURE)) {
    // Insert VM item after Services
    const serviceIndex = navItems.findIndex(item => item.name === 'Services');
    if (serviceIndex !== -1) {
      navItems = [
        ...navItems.slice(0, serviceIndex + 1),
        vmItem,
        ...navItems.slice(serviceIndex + 1)
      ];
    }
  }

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={toggleSidebar}
        ></div>
      )}

      {/* Sidebar */}
      <div
        className={`
          fixed md:sticky top-0 h-full bg-white border-r border-gray-200 flex-shrink-0 w-64
          transform transition-transform duration-300 ease-in-out
          ${isOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'}
          z-50 md:z-0
        `}
      >
        <div className="h-full flex flex-col">
          <div className="h-16 flex items-center justify-between px-4 border-b border-gray-200">
            <Logo />
            <button
              onClick={toggleSidebar}
              className="md:hidden p-2 rounded-md text-gray-500 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <X size={20} />
            </button>
          </div>

          <nav className="flex-1 px-2 py-4 overflow-y-auto">
            <ul className="space-y-1">
              {navItems.map((item) => (
                <li key={item.name}>
                  <NavLink
                    to={item.path}
                    className={({ isActive }) => `
                      flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                      ${isActive
                        ? 'text-blue-700 bg-blue-50'
                        : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
                      }
                    `}
                    end={item.path === '/dashboard'}
                  >
                    <span className="mr-3">{item.icon}</span>
                    {item.name}
                  </NavLink>
                </li>
              ))}
            </ul>
          </nav>
        </div>
      </div>
    </>
  );
};

export default Sidebar;