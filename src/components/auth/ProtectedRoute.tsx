import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
}

/**
 * ProtectedRoute component that handles authentication redirects
 * @param children - The components to render if authentication check passes
 * @param requireAuth - If true, redirects to login if not authenticated
 *                      If false, redirects to dashboard if authenticated
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = true
}) => {
  const { isAuthenticated, loading } = useAuth();
  const location = useLocation();

  // If still loading auth state, show a loading indicator
  if (loading) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>;
  }

  // For routes that require authentication (like dashboard)
  if (requireAuth) {
    // If not authenticated, redirect to login
    if (!isAuthenticated) {
      return <Navigate to="/login" state={{ from: location }} replace />;
    }
    // If authenticated, show the protected content
    return <>{children}</>;
  }
  // For routes that should not be accessed when authenticated (like login/register)
  else {
    // If authenticated, redirect to dashboard
    if (isAuthenticated) {
      return <Navigate to="/dashboard" replace />;
    }
    // If not authenticated, show the content
    return <>{children}</>;
  }
};

export default ProtectedRoute;
