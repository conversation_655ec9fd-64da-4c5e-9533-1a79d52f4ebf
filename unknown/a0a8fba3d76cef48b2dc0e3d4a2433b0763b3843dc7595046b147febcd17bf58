{"pods": [{"id": 1, "name": "<PERSON><PERSON>", "image": "nginx:latest", "status": "running", "cpu": "0.0%", "memory": "241.0 MB", "port": 5678, "config": {"cpu": 2, "memory": 4}, "logs": ["N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true (recommended), or turn this check off set N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=false.", "User settings loaded from: /home/<USER>/.n8n/config", "Initializing n8n process", "n8n ready on 0.0.0.0, port 5678", "Version: 1.76.1", "===============================", "Start Active Workflows:", "===============================", "- \"cronjob clean everyday binlog\" (ID: mxsfw51n2ZcOjgoG)", "  => Started", "- \"Webhook dari Tiket.com\" (ID: 1JyIWRhftiWMTiS)", "  => Started", "", "Editor is now accessible via:", "http://localhost:5678/"]}, {"id": 2, "name": "production-db", "image": "postgres:14-alpine", "status": "running", "cpu": "1.5%", "memory": "512MB", "port": 5432, "config": {"cpu": 2, "memory": 4}, "logs": ["PostgreSQL Database directory appears to contain a database; Skipping initialization", "2024-03-14 10:00:01.123 UTC [1] LOG:  starting PostgreSQL 14.10 on x86_64-pc-linux-musl, compiled by gcc (Alpine 12.2.1_git20220924-r10) 12.2.1 20220924, 64-bit", "2024-03-14 10:00:01.123 UTC [1] LOG:  listening on IPv4 address \"0.0.0.0\", port 5432", "2024-03-14 10:00:01.123 UTC [1] LOG:  listening on IPv6 address \"::\", port 5432", "2024-03-14 10:00:01.125 UTC [1] LOG:  listening on Unix socket \"/var/run/postgresql/.s.PGSQL.5432\"", "2024-03-14 10:00:01.128 UTC [21] LOG:  database system was shut down at 2024-03-14 09:59:59 UTC", "2024-03-14 10:00:01.131 UTC [1] LOG:  database system is ready to accept connections"]}, {"id": 3, "name": "cache-server", "image": "redis:alpine", "status": "running", "cpu": "0.3%", "memory": "64MB", "port": 6379, "config": {"cpu": 1, "memory": 2}, "logs": ["1:C 14 Mar 2024 10:00:01.123 # oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo", "1:C 14 Mar 2024 10:00:01.123 # Redis version=7.2.4, bits=64, commit=00000000, modified=0, pid=1, just started", "1:C 14 Mar 2024 10:00:01.123 # Configuration loaded", "1:M 14 Mar 2024 10:00:01.124 * monotonic clock: POSIX clock_gettime", "1:M 14 Mar 2024 10:00:01.124 * Running mode=standalone, port=6379.", "1:M 14 Mar 2024 10:00:01.124 # Server initialized", "1:M 14 Mar 2024 10:00:01.124 * Ready to accept connections"]}, {"id": 4, "name": "api-service", "image": "node:16", "status": "running", "cpu": "2.1%", "memory": "256MB", "port": 3000, "config": {"cpu": 2, "memory": 4}, "logs": ["> api-service@1.0.0 start", "> node server.js", "", "Server is running on port 3000", "Connected to database successfully", "Initialized middleware", "Loading routes...", "API server is ready to accept requests"]}, {"id": 5, "name": "analytics-db", "image": "mongodb:5.0", "status": "stopped", "cpu": "0%", "memory": "0MB", "port": 27017, "config": {"cpu": 2, "memory": 4}, "logs": ["{\"t\":{\"$date\":\"2024-03-14T10:00:01.123Z\"},\"s\":\"I\",\"c\":\"CONTROL\",\"id\":23285,\"ctx\":\"main\",\"msg\":\"Automatically disabling TLS 1.0, to force-enable TLS 1.0 specify --sslDisabledProtocols 'none'\"}", "{\"t\":{\"$date\":\"2024-03-14T10:00:01.124Z\"},\"s\":\"I\",\"c\":\"NETWORK\",\"id\":4915701,\"ctx\":\"main\",\"msg\":\"Initialized wire specification\",\"attr\":{\"spec\":{\"incomingExternalClient\":{\"minWireVersion\":0,\"maxWireVersion\":13}}}}", "{\"t\":{\"$date\":\"2024-03-14T10:00:01.124Z\"},\"s\":\"I\",\"c\":\"NETWORK\",\"id\":23015,\"ctx\":\"listener\",\"msg\":\"Listening on\",\"attr\":{\"address\":\"/tmp/mongodb-27017.sock\"}}", "{\"t\":{\"$date\":\"2024-03-14T10:00:01.124Z\"},\"s\":\"I\",\"c\":\"NETWORK\",\"id\":23015,\"ctx\":\"listener\",\"msg\":\"Listening on\",\"attr\":{\"address\":\"0.0.0.0\"}}", "{\"t\":{\"$date\":\"2024-03-14T10:00:01.124Z\"},\"s\":\"I\",\"c\":\"STORAGE\",\"id\":22315,\"ctx\":\"initandlisten\",\"msg\":\"MongoDB starting\",\"attr\":{\"pid\":1,\"port\":27017,\"dbPath\":\"/data/db\",\"architecture\":\"64-bit\",\"host\":\"mongodb\"}}", "Container stopped by user"]}]}