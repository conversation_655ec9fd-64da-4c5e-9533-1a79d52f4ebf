import React from 'react';
import { RefreshCw } from 'lucide-react';

interface PodLogsProps {
  logs: string[];
  onRefresh: () => void;
}

const PodLogs: React.FC<PodLogsProps> = ({ logs, onRefresh }) => {
  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200 mt-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-900">Logs</h2>
        <button 
          className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
          onClick={onRefresh}
        >
          <RefreshCw size={16} />
        </button>
      </div>
      <div className="font-mono text-sm whitespace-pre-wrap bg-gray-900 text-gray-100 p-4 rounded-lg overflow-auto max-h-[500px]">
        {logs.map((log, index) => (
          <div key={index} className="py-0.5">{log}</div>
        ))}
      </div>
    </div>
  );
};

export default PodLogs;