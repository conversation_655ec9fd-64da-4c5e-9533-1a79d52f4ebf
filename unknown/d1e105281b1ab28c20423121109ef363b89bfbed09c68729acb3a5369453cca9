import React from 'react';
import { Play, Square, RefreshCw, Trash2, ExternalLink } from 'lucide-react';
import Button from '../common/Button';
import { Pod } from '../../hooks/usePods';

interface PodStatusProps {
  pod: Pod;
  onStart: () => void;
  onStop: () => void;
  onRestart: () => void;
  onDelete: () => void;
  isStarting: boolean;
  isStopping: boolean;
  isRestarting: boolean;
  isDeleting: boolean;
}

const PodStatus: React.FC<PodStatusProps> = ({
  pod,
  onStart,
  onStop,
  onRestart,
  onDelete,
  isStarting,
  isStopping,
  isRestarting,
  isDeleting,
}) => {
  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200 mt-6">
      <div className="flex items-center">
        <div className="flex-1">
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
            pod.status === 'running' 
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-800'
          }`}>
            {pod.status}
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="group relative">
            <Button 
              as="a"
              href={`http://localhost:${pod.port || 8080}`}
              target="_blank"
              variant="secondary" 
              size="sm"
            >
              <ExternalLink size={16} />
            </Button>
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              Open in browser
            </div>
          </div>

          <div className="group relative">
            {pod.status === 'running' ? (
              <Button 
                variant="secondary" 
                size="sm"
                onClick={onStop}
                isLoading={isStopping}
              >
                <Square size={16} />
              </Button>
            ) : (
              <Button 
                variant="secondary" 
                size="sm"
                onClick={onStart}
                isLoading={isStarting}
              >
                <Play size={16} />
              </Button>
            )}
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              {pod.status === 'running' ? 'Stop pod' : 'Start pod'}
            </div>
          </div>

          <div className="group relative">
            <Button 
              variant="secondary" 
              size="sm"
              onClick={onRestart}
              isLoading={isRestarting}
            >
              <RefreshCw size={16} />
            </Button>
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              Restart pod
            </div>
          </div>

          <div className="group relative">
            <Button 
              variant="secondary" 
              size="sm"
              onClick={onDelete}
              isLoading={isDeleting}
            >
              <Trash2 size={16} />
            </Button>
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              Delete pod
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PodStatus;