-- Seed data for billing_plans
INSERT INTO public.billing_plans (id, name, description, price, credits, is_active)
VALUES
    (
        'starter',
        'Starter Pack',
        '500 credits to get you started',
        9.99,
        500,
        true
    ),
    (
        'basic',
        'Basic Pack',
        '1,000 credits for regular users',
        19.99,
        1000,
        true
    ),
    (
        'pro',
        'Pro Pack',
        '3,000 credits for power users',
        49.99,
        3000,
        true
    ),
    (
        'business',
        'Business Pack',
        '10,000 credits for businesses',
        149.99,
        10000,
        true
    ),
    (
        'enterprise',
        'Enterprise Pack',
        '50,000 credits for large organizations',
        499.99,
        50000,
        true
    )
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    price = EXCLUDED.price,
    credits = EXCLUDED.credits,
    is_active = EXCLUDED.is_active,
    updated_at = now();

-- Create a function to seed balances for existing users
CREATE OR REPLACE FUNCTION seed_user_balances() RETURNS void AS $$
DECLARE
    user_record RECORD;
    balance_exists BOOLEAN;
BEGIN
    -- Loop through all users
    FOR user_record IN SELECT id FROM auth.users LOOP
        -- Check if user already has a balance
        SELECT EXISTS (
            SELECT 1 FROM public.user_balances WHERE user_id = user_record.id
        ) INTO balance_exists;
        
        -- If no balance exists, create one with initial credits
        IF NOT balance_exists THEN
            -- Insert balance with 100 free credits
            INSERT INTO public.user_balances (
                user_id, 
                credits
            ) VALUES (
                user_record.id,
                100
            );
            
            -- Record the initial credit transaction
            INSERT INTO public.transactions (
                user_id,
                amount,
                description,
                transaction_type
            ) VALUES (
                user_record.id,
                100,
                'Welcome bonus credits',
                'bonus'
            );
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Execute the function to seed balances
SELECT seed_user_balances();

-- Drop the function after use
DROP FUNCTION seed_user_balances();

-- Create sample transactions and payments for demo purposes
DO $$
DECLARE
    user_record RECORD;
    payment_id BIGINT;
BEGIN
    -- Get the first user for demo data
    SELECT id INTO user_record FROM auth.users LIMIT 1;
    
    -- If we have a user, create some sample transactions and payments
    IF user_record.id IS NOT NULL THEN
        -- Create a sample payment
        INSERT INTO public.payments (
            user_id,
            amount,
            credits,
            status,
            payment_method,
            payment_reference
        ) VALUES (
            user_record.id,
            19.99,
            1000,
            'completed',
            'credit_card',
            'DEMO-PAYMENT-123'
        ) RETURNING id INTO payment_id;
        
        -- Create some sample usage transactions
        INSERT INTO public.transactions (
            user_id,
            amount,
            description,
            transaction_type
        ) VALUES
        (
            user_record.id,
            -10,
            'Pod deployment: nginx',
            'usage'
        ),
        (
            user_record.id,
            -5,
            'Pod deployment: postgres',
            'usage'
        ),
        (
            user_record.id,
            -15,
            'Pod deployment: redis',
            'usage'
        );
    END IF;
END $$;
