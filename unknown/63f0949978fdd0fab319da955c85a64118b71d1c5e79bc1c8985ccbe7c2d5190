import React, { createContext, useContext, ReactNode } from 'react';
import { useProfile as useProfileHook } from '../hooks/useProfile';
import { Profile, ProfileUpdateInput } from '../services/profileService';

interface ProfileContextType {
  profile: Profile | null;
  loading: boolean;
  error: string | null;
  updateProfile: (profileData: ProfileUpdateInput) => Promise<boolean>;
}

const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

export const useProfile = () => {
  const context = useContext(ProfileContext);
  if (context === undefined) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }
  return context;
};

interface ProfileProviderProps {
  children: ReactNode;
}

export const ProfileProvider = ({ children }: ProfileProviderProps) => {
  const profileData = useProfileHook();

  return <ProfileContext.Provider value={profileData}>{children}</ProfileContext.Provider>;
};
