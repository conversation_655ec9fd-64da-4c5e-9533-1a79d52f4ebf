import React from 'react';
import { Box } from 'lucide-react';

interface LogoProps {
  light?: boolean;
}

const Logo: React.FC<LogoProps> = ({ light = false }) => {
  const textColor = light ? 'text-white' : 'text-gray-900';

  return (
    <div className="flex items-center">
      <Box 
        size={28} 
        className="text-blue-600" 
        style={{ 
          filter: 'drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.1))' 
        }} 
      />
      <span className={`ml-2 text-xl font-bold ${textColor}`}>
        Sumo<span className="text-blue-600">Pod</span>
      </span>
    </div>
  );
};

export default Logo;