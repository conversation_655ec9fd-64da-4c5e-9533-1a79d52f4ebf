import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Pod } from '../../hooks/usePods';

interface PodHeaderProps {
  pod: Pod;
}

const PodHeader: React.FC<PodHeaderProps> = ({ pod }) => {
  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200">
      <div className="flex items-center space-x-4">
        <Link 
          to="/dashboard"
          className="inline-flex items-center justify-center p-2 -ml-2 rounded-lg text-gray-500 hover:text-gray-900 hover:bg-gray-100"
        >
          <ArrowLeft size={20} />
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{pod.name}</h1>
          <div className="mt-2 flex items-center space-x-4">
            <span className="text-base text-gray-500">{pod.image}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PodHeader;