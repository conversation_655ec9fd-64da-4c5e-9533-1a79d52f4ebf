import { useState, useEffect } from 'react';
import { profileService, Profile, ProfileUpdateInput } from '../services/profileService';
import { useAuth } from '../context/AuthContext';

export const useProfile = () => {
  const { isAuthenticated, user } = useAuth();
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch profile when authenticated
  useEffect(() => {
    if (!isAuthenticated || !user) {
      setProfile(null);
      setLoading(false);
      return;
    }

    const fetchProfile = async () => {
      try {
        setLoading(true);
        const data = await profileService.getProfile();
        setProfile(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching profile:', err);
        setError('Failed to load profile');
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, [isAuthenticated, user]);

  const updateProfile = async (profileData: ProfileUpdateInput): Promise<boolean> => {
    if (!isAuthenticated || !user) {
      setError('You must be logged in to update your profile');
      return false;
    }

    try {
      setLoading(true);
      
      // Update user metadata in auth
      await profileService.updateUserMetadata({
        firstName: profileData.firstName,
        lastName: profileData.lastName,
        avatar_url: profileData.avatarUrl,
        company: profileData.company
      });
      
      // Update profile in database
      const updatedProfile = await profileService.updateProfile(profileData);
      setProfile(updatedProfile);
      setError(null);
      return true;
    } catch (err) {
      console.error('Error updating profile:', err);
      setError('Failed to update profile');
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    profile,
    loading,
    error,
    updateProfile
  };
};
