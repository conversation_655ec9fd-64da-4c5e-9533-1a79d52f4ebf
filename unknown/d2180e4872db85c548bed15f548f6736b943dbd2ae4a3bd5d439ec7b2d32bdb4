-- Create billing_plans table
CREATE TABLE IF NOT EXISTS public.billing_plans (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    price NUMERIC(10, 2) NOT NULL,
    credits INTEGER NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create user_balances table
CREATE TABLE IF NOT EXISTS public.user_balances (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    credits INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(user_id)
);

-- Create transactions table
CREATE TABLE IF NOT EXISTS public.transactions (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    amount INTEGER NOT NULL,
    description TEXT NOT NULL,
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('purchase', 'usage', 'refund', 'bonus')),
    reference_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create payments table
CREATE TABLE IF NOT EXISTS public.payments (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    amount NUMERIC(10, 2) NOT NULL,
    credits INTEGER NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('completed', 'pending', 'failed')),
    payment_method TEXT,
    payment_reference TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Enable Row Level Security
ALTER TABLE public.billing_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_balances ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for billing_plans
-- Everyone can view active billing plans
CREATE POLICY "Everyone can view active billing plans" 
    ON public.billing_plans 
    FOR SELECT 
    USING (is_active = true);

-- Only admins can modify billing plans (we'll need to add an admin check later)
CREATE POLICY "No one can insert billing plans" 
    ON public.billing_plans 
    FOR INSERT 
    WITH CHECK (false);

CREATE POLICY "No one can update billing plans" 
    ON public.billing_plans 
    FOR UPDATE 
    USING (false);

CREATE POLICY "No one can delete billing plans" 
    ON public.billing_plans 
    FOR DELETE 
    USING (false);

-- Create RLS policies for user_balances
-- Users can only view their own balance
CREATE POLICY "Users can view their own balance" 
    ON public.user_balances 
    FOR SELECT 
    USING (auth.uid() = user_id);

-- Only service role can insert/update/delete balances
CREATE POLICY "No one can insert balances" 
    ON public.user_balances 
    FOR INSERT 
    WITH CHECK (false);

CREATE POLICY "No one can update balances" 
    ON public.user_balances 
    FOR UPDATE 
    USING (false);

CREATE POLICY "No one can delete balances" 
    ON public.user_balances 
    FOR DELETE 
    USING (false);

-- Create RLS policies for transactions
-- Users can only view their own transactions
CREATE POLICY "Users can view their own transactions" 
    ON public.transactions 
    FOR SELECT 
    USING (auth.uid() = user_id);

-- Only service role can insert/update/delete transactions
CREATE POLICY "No one can insert transactions" 
    ON public.transactions 
    FOR INSERT 
    WITH CHECK (false);

CREATE POLICY "No one can update transactions" 
    ON public.transactions 
    FOR UPDATE 
    USING (false);

CREATE POLICY "No one can delete transactions" 
    ON public.transactions 
    FOR DELETE 
    USING (false);

-- Create RLS policies for payments
-- Users can only view their own payments
CREATE POLICY "Users can view their own payments" 
    ON public.payments 
    FOR SELECT 
    USING (auth.uid() = user_id);

-- Only service role can insert/update/delete payments
CREATE POLICY "No one can insert payments" 
    ON public.payments 
    FOR INSERT 
    WITH CHECK (false);

CREATE POLICY "No one can update payments" 
    ON public.payments 
    FOR UPDATE 
    USING (false);

CREATE POLICY "No one can delete payments" 
    ON public.payments 
    FOR DELETE 
    USING (false);

-- Create indexes
CREATE INDEX IF NOT EXISTS user_balances_user_id_idx ON public.user_balances (user_id);
CREATE INDEX IF NOT EXISTS transactions_user_id_idx ON public.transactions (user_id);
CREATE INDEX IF NOT EXISTS transactions_type_idx ON public.transactions (transaction_type);
CREATE INDEX IF NOT EXISTS payments_user_id_idx ON public.payments (user_id);
CREATE INDEX IF NOT EXISTS payments_status_idx ON public.payments (status);

-- Add triggers for updated_at
CREATE TRIGGER update_billing_plans_updated_at
BEFORE UPDATE ON public.billing_plans
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_user_balances_updated_at
BEFORE UPDATE ON public.user_balances
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_payments_updated_at
BEFORE UPDATE ON public.payments
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Create function to create user balance when a new user is created
CREATE OR REPLACE FUNCTION public.handle_new_user_balance()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_balances (user_id, credits)
  VALUES (NEW.id, 100); -- Give 100 free credits to new users
  
  -- Record the initial credit transaction
  INSERT INTO public.transactions (user_id, amount, description, transaction_type)
  VALUES (NEW.id, 100, 'Welcome bonus credits', 'bonus');
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create user balance for new users
CREATE TRIGGER on_auth_user_created_balance
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user_balance();

-- Create function to update user balance when a transaction occurs
CREATE OR REPLACE FUNCTION public.update_user_balance_on_transaction()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the user's balance based on the transaction
  UPDATE public.user_balances
  SET 
    credits = credits + NEW.amount,
    updated_at = now()
  WHERE user_id = NEW.user_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically update user balance when a transaction is created
CREATE TRIGGER on_transaction_created
  AFTER INSERT ON public.transactions
  FOR EACH ROW
  EXECUTE FUNCTION public.update_user_balance_on_transaction();

-- Create function to create a transaction when a payment is completed
CREATE OR REPLACE FUNCTION public.create_transaction_on_payment()
RETURNS TRIGGER AS $$
BEGIN
  -- Only create a transaction if the payment status is 'completed'
  IF NEW.status = 'completed' THEN
    -- Insert a transaction record
    INSERT INTO public.transactions (
      user_id, 
      amount, 
      description, 
      transaction_type,
      reference_id
    ) VALUES (
      NEW.user_id,
      NEW.credits,
      'Credit purchase: ' || NEW.credits || ' credits',
      'purchase',
      'payment_' || NEW.id
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create a transaction when a payment is completed
CREATE TRIGGER on_payment_completed
  AFTER INSERT OR UPDATE ON public.payments
  FOR EACH ROW
  WHEN (NEW.status = 'completed')
  EXECUTE FUNCTION public.create_transaction_on_payment();
