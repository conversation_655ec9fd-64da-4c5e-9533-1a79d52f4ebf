-- Seed data for billing_plans
INSERT INTO public.billing_plans (id, name, description, price, interval, features, is_active)
VALUES
    (
        'free',
        'Free Plan',
        'Basic features for personal use',
        0.00,
        'monthly',
        jsonb_build_object(
            'pods', 3,
            'storage', '5GB',
            'bandwidth', '10GB',
            'support', 'Community'
        ),
        true
    ),
    (
        'pro',
        'Pro Plan',
        'Advanced features for professionals',
        19.99,
        'monthly',
        jsonb_build_object(
            'pods', 10,
            'storage', '20GB',
            'bandwidth', '50GB',
            'support', 'Priority',
            'custom_domains', true,
            'team_members', 3
        ),
        true
    ),
    (
        'pro_yearly',
        'Pro Plan (Yearly)',
        'Advanced features for professionals with yearly discount',
        199.99,
        'yearly',
        jsonb_build_object(
            'pods', 10,
            'storage', '20GB',
            'bandwidth', '50GB',
            'support', 'Priority',
            'custom_domains', true,
            'team_members', 3
        ),
        true
    ),
    (
        'enterprise',
        'Enterprise Plan',
        'Complete solution for businesses',
        99.99,
        'monthly',
        jsonb_build_object(
            'pods', 'Unlimited',
            'storage', '100GB',
            'bandwidth', '500GB',
            'support', 'Dedicated',
            'custom_domains', true,
            'team_members', 'Unlimited',
            'sla', '99.9%',
            'audit_logs', true,
            'sso', true
        ),
        true
    ),
    (
        'enterprise_yearly',
        'Enterprise Plan (Yearly)',
        'Complete solution for businesses with yearly discount',
        999.99,
        'yearly',
        jsonb_build_object(
            'pods', 'Unlimited',
            'storage', '100GB',
            'bandwidth', '500GB',
            'support', 'Dedicated',
            'custom_domains', true,
            'team_members', 'Unlimited',
            'sla', '99.9%',
            'audit_logs', true,
            'sso', true
        ),
        true
    )
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    price = EXCLUDED.price,
    interval = EXCLUDED.interval,
    features = EXCLUDED.features,
    is_active = EXCLUDED.is_active,
    updated_at = now();

-- Create a function to seed subscriptions for existing users
CREATE OR REPLACE FUNCTION seed_user_subscriptions() RETURNS void AS $$
DECLARE
    user_record RECORD;
    subscription_exists BOOLEAN;
    current_date TIMESTAMP WITH TIME ZONE := now();
    period_end TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Loop through all users
    FOR user_record IN SELECT id FROM auth.users LOOP
        -- Check if user already has a subscription
        SELECT EXISTS (
            SELECT 1 FROM public.subscriptions WHERE user_id = user_record.id
        ) INTO subscription_exists;
        
        -- If no subscription exists, create a free subscription
        IF NOT subscription_exists THEN
            -- Set period end to 1 year from now
            period_end := current_date + INTERVAL '1 year';
            
            -- Insert free subscription
            INSERT INTO public.subscriptions (
                user_id, 
                plan_id, 
                status, 
                current_period_start, 
                current_period_end
            ) VALUES (
                user_record.id,
                'free',
                'active',
                current_date,
                period_end
            );
            
            -- Create a sample invoice for the free plan (even though it's $0)
            INSERT INTO public.invoices (
                user_id,
                subscription_id,
                amount,
                status,
                invoice_date,
                due_date,
                invoice_number
            ) VALUES (
                user_record.id,
                currval('public.subscriptions_id_seq'),
                0.00,
                'paid',
                current_date,
                current_date,
                'INV-' || to_char(current_date, 'YYYYMMDD') || '-' || currval('public.subscriptions_id_seq')
            );
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Execute the function to seed subscriptions
SELECT seed_user_subscriptions();

-- Drop the function after use
DROP FUNCTION seed_user_subscriptions();
