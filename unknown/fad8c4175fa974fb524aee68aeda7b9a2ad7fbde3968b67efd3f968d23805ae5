import { useState, useMemo, useEffect } from 'react';
import { podService } from '../services/podService';

export interface Pod {
  id: number;
  name: string;
  image: string;
  status: 'running' | 'stopped';
  cpu: string;
  memory: string;
  logs?: string[];
  port?: number;
  config: {
    cpu: number;
    memory: number;
  };
}

export const usePods = (id?: string) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'running' | 'stopped'>('all');
  const [pods, setPods] = useState<Pod[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [singlePod, setSinglePod] = useState<Pod | null>(null);

  // Fetch all pods
  useEffect(() => {
    const fetchPods = async () => {
      try {
        setLoading(true);
        const data = await podService.getPods();
        setPods(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching pods:', err);
        setError('Failed to load pods');
      } finally {
        setLoading(false);
      }
    };

    fetchPods();
  }, []);

  // Fetch single pod if ID is provided
  useEffect(() => {
    if (!id) {
      setSinglePod(null);
      return;
    }

    const fetchPod = async () => {
      try {
        setLoading(true);
        const data = await podService.getPodById(parseInt(id));
        setSinglePod(data);
        setError(null);
      } catch (err) {
        console.error(`Error fetching pod with id ${id}:`, err);
        setError('Failed to load pod details');
      } finally {
        setLoading(false);
      }
    };

    fetchPod();
  }, [id]);

  const filteredPods = useMemo(() => {
    return pods.filter(pod => {
      const matchesStatus = statusFilter === 'all' || pod.status === statusFilter;
      const matchesSearch = searchQuery === '' ||
        pod.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        pod.image.toLowerCase().includes(searchQuery.toLowerCase());

      return matchesStatus && matchesSearch;
    });
  }, [pods, statusFilter, searchQuery]);

  return {
    pods: filteredPods,
    pod: singlePod,
    searchQuery,
    setSearchQuery,
    statusFilter,
    setStatusFilter,
    loading,
    error,
    totalPods: pods.length,
    runningPods: pods.filter(pod => pod.status === 'running').length,
    stoppedPods: pods.filter(pod => pod.status === 'stopped').length
  };
};