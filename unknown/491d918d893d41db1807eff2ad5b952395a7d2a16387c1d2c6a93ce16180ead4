import { Transaction } from '../types/billing';

export const transactions: Transaction[] = [
  { 
    id: 1,
    date: 'Mar 15, 2024',
    type: 'Top-up',
    amount: '+$200.00',
    status: 'Paid',
    balance: '$345.50',
    downloadUrl: '#'
  },
  { 
    id: 2,
    date: 'Mar 10, 2024',
    type: 'Top-up',
    amount: '+$100.00',
    status: 'Unpaid',
    balance: '$145.50',
    downloadUrl: '#'
  },
  { 
    id: 3,
    date: 'Mar 5, 2024',
    type: 'Usage',
    amount: '-$4.50',
    status: 'Paid',
    balance: '$45.50',
    downloadUrl: '#'
  },
  { 
    id: 4,
    date: 'Mar 1, 2024',
    type: 'Usage',
    amount: '-$25.50',
    status: 'Paid',
    balance: '$50.00',
    downloadUrl: '#'
  },
  { 
    id: 5,
    date: 'Feb 25, 2024',
    type: 'Usage',
    amount: '-$15.00',
    status: 'Paid',
    balance: '$75.50',
    downloadUrl: '#'
  },
  { 
    id: 6,
    date: 'Feb 20, 2024',
    type: 'Usage',
    amount: '-$10.00',
    status: 'Paid',
    balance: '$90.50',
    downloadUrl: '#'
  },
  { 
    id: 7,
    date: 'Feb 15, 2024',
    type: 'Usage',
    amount: '-$20.00',
    status: 'Paid',
    balance: '$100.50',
    downloadUrl: '#'
  },
  { 
    id: 8,
    date: 'Feb 10, 2024',
    type: 'Usage',
    amount: '-$30.00',
    status: 'Paid',
    balance: '$120.50',
    downloadUrl: '#'
  },
  { 
    id: 9,
    date: 'Feb 5, 2024',
    type: 'Usage',
    amount: '-$25.00',
    status: 'Paid',
    balance: '$150.50',
    downloadUrl: '#'
  },
  { 
    id: 10,
    date: 'Feb 1, 2024',
    type: 'Usage',
    amount: '-$24.50',
    status: 'Paid',
    balance: '$175.50',
    downloadUrl: '#'
  }
];