import React from 'react';
import { Package } from 'lucide-react';
import Button from './Button';
import { Link } from 'react-router-dom';

interface CreatePodButtonProps {
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
}

const CreatePodButton: React.FC<CreatePodButtonProps> = ({ 
  variant = 'primary',
  size = 'md'
}) => {
  return (
    <Button 
      as={Link} 
      to="/dashboard/pods/create"
      variant={variant} 
      size={size}
    >
      <Package size={size === 'sm' ? 16 : 18} className="mr-2" />
      Create Pod
    </Button>
  );
};

export default CreatePodButton;