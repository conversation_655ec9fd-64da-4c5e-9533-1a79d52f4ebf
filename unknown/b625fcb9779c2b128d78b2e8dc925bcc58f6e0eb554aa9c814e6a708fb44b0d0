import { useState, useEffect, useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import { transactions } from '../data/transactions';
import { Transaction, TransactionResponse } from '../types/billing';

export const useTransactions = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const page = parseInt(searchParams.get('page') || '1');
  const perPage = 5;

  const paginatedData = useMemo((): TransactionResponse => {
    const start = (page - 1) * perPage;
    const end = start + perPage;
    
    return {
      data: transactions.slice(start, end),
      total: transactions.length,
      page,
      perPage,
      totalPages: Math.ceil(transactions.length / perPage)
    };
  }, [page]);

  const setPage = (newPage: number) => {
    setSearchParams({ page: newPage.toString() });
  };

  return {
    ...paginatedData,
    setPage
  };
};