# Balance-Based Billing System for SumoPod

This document explains the balance-based billing system implemented for SumoPod.

## Overview

SumoPod uses a credit-based billing system where:

1. Users purchase credits that are added to their balance
2. Credits are consumed when users perform actions (like deploying pods)
3. Users can view their transaction history and current balance
4. New users receive 100 free credits to get started

## Database Schema

The billing system uses the following tables:

### 1. billing_plans

Stores the available credit packages that users can purchase.

- `id`: Unique identifier for the plan
- `name`: Display name of the plan
- `description`: Description of what the plan offers
- `price`: Cost in USD
- `credits`: Number of credits the user receives
- `is_active`: Whether the plan is currently available for purchase

### 2. user_balances

Tracks each user's current credit balance.

- `id`: Unique identifier for the balance record
- `user_id`: Reference to the auth.users table
- `credits`: Current number of credits available
- `created_at`: When the balance was first created
- `updated_at`: When the balance was last updated

### 3. transactions

Records all credit transactions (purchases, usage, refunds, bonuses).

- `id`: Unique identifier for the transaction
- `user_id`: Reference to the auth.users table
- `amount`: Number of credits (positive for additions, negative for deductions)
- `description`: Description of the transaction
- `transaction_type`: Type of transaction (purchase, usage, refund, bonus)
- `reference_id`: Optional reference to external systems
- `created_at`: When the transaction occurred

### 4. payments

Records payment information for credit purchases.

- `id`: Unique identifier for the payment
- `user_id`: Reference to the auth.users table
- `amount`: Amount paid in USD
- `credits`: Number of credits purchased
- `status`: Payment status (completed, pending, failed)
- `payment_method`: Method used for payment
- `payment_reference`: Reference to external payment system
- `created_at`: When the payment was created
- `updated_at`: When the payment was last updated

## Triggers and Automations

The system includes several triggers to automate common operations:

1. `handle_new_user_balance`: Creates a balance record with 100 free credits when a new user registers
2. `update_user_balance_on_transaction`: Updates the user's balance when a transaction is recorded
3. `create_transaction_on_payment`: Creates a transaction record when a payment is completed

## Row Level Security (RLS)

Row Level Security is implemented to ensure users can only access their own data:

- Users can only view their own balance, transactions, and payments
- Everyone can view active billing plans
- Only service role can modify any of the tables

## How to Apply Migrations

To set up the billing system in your Supabase project:

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Copy the contents of `migrations/20250507_balance_billing_schema.sql`
4. Paste into the SQL Editor and run the query
5. Copy the contents of `seed_balance_billing.sql`
6. Paste into the SQL Editor and run the query

## Integration with the Frontend

The billing system is integrated with the frontend through:

1. `billingService.ts`: Service for interacting with the billing API
2. `useBilling.ts`: Custom hook for managing billing state
3. `Billing.tsx`: Page component for displaying and managing billing

## Usage Costs

You can define the cost of different actions in your application. For example:

- Deploying a basic pod: 10 credits
- Deploying a database pod: 20 credits
- Storage usage: 1 credit per GB per day

To deduct credits, create a transaction with a negative amount:

```typescript
// Example of how to deduct credits for pod deployment
async function deployPod(podType: string) {
  const cost = podType === 'database' ? 20 : 10;
  
  // Create a transaction to deduct credits
  const { error } = await supabase
    .from('transactions')
    .insert([
      {
        user_id: userId,
        amount: -cost,
        description: `Pod deployment: ${podType}`,
        transaction_type: 'usage'
      }
    ]);
    
  if (error) {
    throw new Error('Failed to deduct credits');
  }
  
  // The user's balance will be automatically updated by the trigger
}
```

## Testing the Billing System

To test the billing system:

1. Register a new user (they will automatically receive 100 free credits)
2. Navigate to the Billing page to see your current balance
3. Purchase additional credits using one of the available plans
4. Deploy pods or perform other actions to see credits being deducted
5. Check your transaction history to see all credit movements
