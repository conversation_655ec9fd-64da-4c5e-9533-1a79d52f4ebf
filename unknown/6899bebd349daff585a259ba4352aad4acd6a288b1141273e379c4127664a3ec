import { useState, useMemo, useEffect } from 'react';
import * as Icons from 'lucide-react';
import { Box } from 'lucide-react';
import { templateService, Template as TemplateData } from '../services/templateService';

export interface Template extends Omit<TemplateData, 'iconName'> {
  icon: React.ReactNode;
}

export const useTemplates = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [templatesData, setTemplatesData] = useState<TemplateData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch templates
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setLoading(true);
        const data = await templateService.getTemplates();
        setTemplatesData(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching templates:', err);
        setError('Failed to load templates');
      } finally {
        setLoading(false);
      }
    };

    fetchTemplates();
  }, []);

  const templates = useMemo(() => {
    return templatesData.map(template => {
      const IconComponent = Icons[template.iconName as keyof typeof Icons];

      const icon = typeof IconComponent === 'function'
        ? <IconComponent className="h-8 w-8 text-blue-600" />
        : <Box className="h-8 w-8 text-blue-600" />;

      return {
        ...template,
        icon
      };
    });
  }, [templatesData]);

  const filteredTemplates = useMemo(() => {
    return templates.filter(template => {
      return searchQuery === '' ||
        template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    });
  }, [templates, searchQuery]);

  return {
    templates: filteredTemplates,
    searchQuery,
    setSearchQuery,
    loading,
    error
  };
};