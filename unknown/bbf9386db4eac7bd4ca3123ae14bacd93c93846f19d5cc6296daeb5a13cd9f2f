import React from 'react';
import { Play, Square, RefreshCw, Trash } from 'lucide-react';
import Button from '../common/Button';
import { Pod } from '../../hooks/usePods';

interface PodActionsProps {
  pod: Pod;
  onStart: () => void;
  onStop: () => void;
  onRestart: () => void;
  onDelete: () => void;
  isStarting: boolean;
  isStopping: boolean;
  isRestarting: boolean;
  isDeleting: boolean;
}

const PodActions: React.FC<PodActionsProps> = ({
  pod,
  onStart,
  onStop,
  onRestart,
  onDelete,
  isStarting,
  isStopping,
  isRestarting,
  isDeleting,
}) => {
  return (
    <div className="flex items-center space-x-2">
      {pod.status === 'running' ? (
        <Button 
          variant="secondary" 
          size="sm"
          onClick={onStop}
          isLoading={isStopping}
        >
          <Square size={16} className="mr-2" />
          Stop
        </Button>
      ) : (
        <Button 
          variant="secondary" 
          size="sm"
          onClick={onStart}
          isLoading={isStarting}
        >
          <Play size={16} className="mr-2" />
          Start
        </Button>
      )}
      <Button 
        variant="secondary" 
        size="sm"
        onClick={onRestart}
        isLoading={isRestarting}
      >
        <RefreshCw size={16} className="mr-2" />
        Restart
      </Button>
      <Button 
        variant="secondary" 
        size="sm"
        onClick={onDelete}
        isLoading={isDeleting}
      >
        <Trash size={16} className="mr-2" />
        Delete
      </Button>
    </div>
  );
};

export default PodActions;