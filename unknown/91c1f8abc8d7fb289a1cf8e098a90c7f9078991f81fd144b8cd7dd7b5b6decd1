import { supabase } from '../lib/supabase';
import { Pod } from '../hooks/usePods';

export interface PodCreateInput {
  name: string;
  image: string;
  config: {
    cpu: number;
    memory: number;
  };
}

export const podService = {
  async getPods(): Promise<Pod[]> {
    // With RLS enabled, this will only return pods belonging to the authenticated user
    const { data, error } = await supabase
      .from('pods')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching pods:', error);
      throw error;
    }

    return data || [];
  },

  async getPodById(id: number): Promise<Pod | null> {
    // With RLS enabled, this will only return the pod if it belongs to the authenticated user
    const { data, error } = await supabase
      .from('pods')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Error fetching pod with id ${id}:`, error);
      return null;
    }

    return data;
  },

  async createPod(pod: PodCreateInput): Promise<Pod | null> {
    // Get the current user's ID
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      throw new Error('User must be logged in to create a pod');
    }

    const { data, error } = await supabase
      .from('pods')
      .insert([
        {
          ...pod,
          status: 'stopped',
          cpu: '0%',
          memory: '0 MB',
          user_id: session.user.id, // Set the user_id to the current user's ID
          logs: [] // Initialize with empty logs array
        }
      ])
      .select()
      .single();

    if (error) {
      console.error('Error creating pod:', error);
      throw error;
    }

    return data;
  },

  async updatePodStatus(id: number, status: 'running' | 'stopped'): Promise<void> {
    // With RLS enabled, this will only update the pod if it belongs to the authenticated user
    const { error } = await supabase
      .from('pods')
      .update({ status })
      .eq('id', id);

    if (error) {
      console.error(`Error updating pod status for id ${id}:`, error);
      throw error;
    }
  },

  async updatePodLogs(id: number, logs: string[]): Promise<void> {
    // With RLS enabled, this will only update the pod if it belongs to the authenticated user
    const { error } = await supabase
      .from('pods')
      .update({ logs })
      .eq('id', id);

    if (error) {
      console.error(`Error updating pod logs for id ${id}:`, error);
      throw error;
    }
  },

  async deletePod(id: number): Promise<void> {
    // With RLS enabled, this will only delete the pod if it belongs to the authenticated user
    const { error } = await supabase
      .from('pods')
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Error deleting pod with id ${id}:`, error);
      throw error;
    }
  }
};
