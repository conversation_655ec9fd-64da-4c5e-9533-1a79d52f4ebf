import { supabase } from '../lib/supabase';

export interface Template {
  id: string;
  name: string;
  description: string;
  iconName: string;
  tags: string[];
  category: string;
  price: number;
  github: string;
  website: string;
}

export const templateService = {
  async getTemplates(): Promise<Template[]> {
    // With RLS enabled, this will be accessible to all users
    const { data, error } = await supabase
      .from('templates')
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching templates:', error);
      throw error;
    }

    // Map database column names to our interface
    return (data || []).map(item => ({
      id: item.id,
      name: item.name,
      description: item.description,
      iconName: item.icon_name,
      tags: item.tags,
      category: item.category,
      price: item.price,
      github: item.github,
      website: item.website
    }));
  },

  async getTemplateById(id: string): Promise<Template | null> {
    // With RLS enabled, this will be accessible to all users
    const { data, error } = await supabase
      .from('templates')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Error fetching template with id ${id}:`, error);
      return null;
    }

    if (!data) return null;

    // Map database column names to our interface
    return {
      id: data.id,
      name: data.name,
      description: data.description,
      iconName: data.icon_name,
      tags: data.tags,
      category: data.category,
      price: data.price,
      github: data.github,
      website: data.website
    };
  },

  async getTemplatesByCategory(category: string): Promise<Template[]> {
    // With RLS enabled, this will be accessible to all users
    const { data, error } = await supabase
      .from('templates')
      .select('*')
      .eq('category', category)
      .order('name');

    if (error) {
      console.error(`Error fetching templates for category ${category}:`, error);
      throw error;
    }

    // Map database column names to our interface
    return (data || []).map(item => ({
      id: item.id,
      name: item.name,
      description: item.description,
      iconName: item.icon_name,
      tags: item.tags,
      category: item.category,
      price: item.price,
      github: item.github,
      website: item.website
    }));
  }
};
