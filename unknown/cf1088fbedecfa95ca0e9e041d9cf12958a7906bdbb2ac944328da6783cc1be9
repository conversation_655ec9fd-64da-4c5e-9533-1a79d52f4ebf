import React from 'react';
import { Cpu, Memory } from 'lucide-react';
import { Pod } from '../../hooks/usePods';

interface PodConfigProps {
  pod: Pod;
}

const PodConfig: React.FC<PodConfigProps> = ({ pod }) => {
  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200 mt-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Configuration</h2>
      <div className="grid grid-cols-2 gap-6">
        <div className="flex items-center">
          <div className="h-10 w-10 bg-blue-50 rounded-lg flex items-center justify-center">
            <Cpu className="h-5 w-5 text-blue-600" />
          </div>
          <div className="ml-3">
            <div className="text-sm text-gray-500">CPU</div>
            <div className="text-lg font-semibold text-gray-900">{pod.config.cpu} Core</div>
          </div>
        </div>
        <div className="flex items-center">
          <div className="h-10 w-10 bg-blue-50 rounded-lg flex items-center justify-center">
            <Memory className="h-5 w-5 text-blue-600" />
          </div>
          <div className="ml-3">
            <div className="text-sm text-gray-500">Memory</div>
            <div className="text-lg font-semibold text-gray-900">{pod.config.memory}GB RAM</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PodConfig;