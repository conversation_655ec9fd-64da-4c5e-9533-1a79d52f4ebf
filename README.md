# SumoPod

SumoPod is a container and application management platform that allows users to easily deploy and manage containerized applications.

## Features

- User authentication with email/password
- Browse application templates
- Deploy applications from templates
- Manage running containers
- Monitor container status and logs

## Tech Stack

- React with TypeScript
- Vite for building
- Tailwind CSS for styling
- Supabase for backend (authentication, database)

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or bun

### Installation

1. Clone the repository:

```bash
git clone https://github.com/yourusername/sumopod.git
cd sumopod
```

2. Install dependencies:

```bash
bun install
```

3. Create a `.env` file in the root directory with your Supabase credentials and feature flags:

```
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key

# Feature flags
VITE_PODS_FEATURE=true
VITE_VM_FEATURE=true
```

Feature flags control which features are enabled in the application:
- `VITE_PODS_FEATURE`: When set to `true`, enables the Pods feature and shows it in the sidebar
- `VITE_VM_FEATURE`: When set to `true`, enables the VMs feature and shows it in the sidebar

4. Set up the database:

```bash
bun run db:setup
```

5. Start the development server:

```bash
bun run dev
```

## Database Setup

The application uses Supabase as its backend. The database schema includes:

- `pods` table: Stores information about user's pods
- `templates` table: Stores information about available pod templates

Row Level Security (RLS) is enabled to ensure users can only access their own data.

For more details on the database setup, see the [supabase/README.md](supabase/README.md) file.

## Authentication

The application uses Supabase Authentication for user management. Features include:

- Email/password authentication
- Password reset
- User profile data storage

## Development

### Project Structure

- `src/components`: React components
- `src/context`: React context providers
- `src/hooks`: Custom React hooks
- `src/lib`: Utility functions and libraries
- `src/pages`: Page components
- `src/services`: API services
- `supabase`: Database migrations and seed data

### Available Scripts

- `bun run dev`: Start the development server
- `bun run build`: Build the application for production
- `bun run preview`: Preview the production build
- `bun run lint`: Run ESLint
- `bun run db:setup`: Set up the database schema and seed data

## Deployment

### Deploying to Netlify

This project is configured for easy deployment to Netlify. The following files are included:

- `netlify.toml`: Configuration file for Netlify build settings and redirects
- `public/_redirects`: Ensures that client-side routing works correctly
- `netlify.sh`: Helper script for building and deploying

To deploy to Netlify:

1. Push your code to a Git repository (GitHub, GitLab, or Bitbucket)
2. Log in to Netlify and click "New site from Git"
3. Select your repository and configure the following settings:
   - Build command: `bun run build`
   - Publish directory: `dist`
4. Add your environment variables in the Netlify dashboard:
   - `VITE_SUPABASE_URL`
   - `VITE_SUPABASE_ANON_KEY`
5. Click "Deploy site"

Alternatively, you can use the Netlify CLI:

```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login to Netlify
netlify login

# Initialize a new Netlify site
netlify init

# Deploy to Netlify
netlify deploy --prod
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
