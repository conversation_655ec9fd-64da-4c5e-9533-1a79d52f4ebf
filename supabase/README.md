# Supabase Setup for SumoPod

This directory contains the SQL migrations and seed data for setting up the Supabase backend for SumoPod.

## Database Schema

The SumoPod application uses the following tables:

1. **pods** - Stores information about user's pods
2. **templates** - Stores information about available pod templates

## Row Level Security (RLS)

Row Level Security is enabled for all tables to ensure users can only access their own data:

- **pods**: Users can only view, insert, update, and delete their own pods
- **templates**: Everyone can view templates, but only admins can modify them (currently disabled)

## How to Apply Migrations

### Option 1: Using the Supabase Dashboard

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Copy the contents of `migrations/20250505_initial_schema.sql`
4. <PERSON><PERSON> into the SQL Editor and run the query
5. Copy the contents of `seed.sql`
6. <PERSON>e into the SQL Editor and run the query

### Option 2: Using the Supabase CLI

If you have the Supabase CLI installed, you can run:

```bash
# Link to your Supabase project
supabase link --project-ref dhsrwbufpdvuptdzeieo

# Apply migrations
supabase db push

# Apply seed data
supabase db execute < seed.sql
```

## Verifying Setup

After applying the migrations and seed data, you should:

1. See the `pods` and `templates` tables in your database
2. The `templates` table should be populated with initial data
3. Row Level Security should be enabled for both tables

## Troubleshooting

If you encounter any issues:

1. Check that the SQL was executed without errors
2. Verify that the tables were created correctly
3. Ensure that RLS policies are in place
4. Test the policies by trying to access data as different users
