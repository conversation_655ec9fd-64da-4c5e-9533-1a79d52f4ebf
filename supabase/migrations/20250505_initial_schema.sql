-- Create profiles table
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    first_name TEXT,
    last_name TEXT,
    full_name TEXT GENERATED ALWAYS AS (
        CASE
            WHEN first_name IS NULL AND last_name IS NULL THEN NULL
            WHEN first_name IS NULL THEN last_name
            WHEN last_name IS NULL THEN first_name
            ELSE first_name || ' ' || last_name
        END
    ) STORED,
    avatar_url TEXT,
    company TEXT,
    website TEXT,
    billing_address JSONB,
    subscription_tier TEXT DEFAULT 'free',
    subscription_status TEXT DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create pods table
CREATE TABLE IF NOT EXISTS public.pods (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    image TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('running', 'stopped')),
    cpu TEXT NOT NULL,
    memory TEXT NOT NULL,
    port INTEGER,
    logs TEXT[],
    config JSONB NOT NULL,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create templates table
CREATE TABLE IF NOT EXISTS public.templates (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    icon_name TEXT NOT NULL,
    tags TEXT[] NOT NULL,
    category TEXT NOT NULL,
    price NUMERIC(10, 2) NOT NULL,
    github TEXT,
    website TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Enable Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pods ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.templates ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for pods
-- Users can only view their own pods
CREATE POLICY "Users can view their own pods"
    ON public.pods
    FOR SELECT
    USING (auth.uid() = user_id);

-- Users can only insert their own pods
CREATE POLICY "Users can insert their own pods"
    ON public.pods
    FOR INSERT
    WITH CHECK (auth.uid() = user_id);

-- Users can only update their own pods
CREATE POLICY "Users can update their own pods"
    ON public.pods
    FOR UPDATE
    USING (auth.uid() = user_id);

-- Users can only delete their own pods
CREATE POLICY "Users can delete their own pods"
    ON public.pods
    FOR DELETE
    USING (auth.uid() = user_id);

-- Create RLS policies for profiles
-- Users can only view their own profile
CREATE POLICY "Users can view their own profile"
    ON public.profiles
    FOR SELECT
    USING (auth.uid() = id);

-- Users can only update their own profile
CREATE POLICY "Users can update their own profile"
    ON public.profiles
    FOR UPDATE
    USING (auth.uid() = id);

-- Create RLS policies for templates
-- Everyone can view templates
CREATE POLICY "Everyone can view templates"
    ON public.templates
    FOR SELECT
    USING (true);

-- Only admins can modify templates (we'll need to add an admin check later)
-- For now, no one can modify templates through the API
CREATE POLICY "No one can insert templates"
    ON public.templates
    FOR INSERT
    WITH CHECK (false);

CREATE POLICY "No one can update templates"
    ON public.templates
    FOR UPDATE
    USING (false);

CREATE POLICY "No one can delete templates"
    ON public.templates
    FOR DELETE
    USING (false);

-- Create indexes
CREATE INDEX IF NOT EXISTS pods_user_id_idx ON public.pods (user_id);
CREATE INDEX IF NOT EXISTS pods_status_idx ON public.pods (status);
CREATE INDEX IF NOT EXISTS templates_category_idx ON public.templates (category);
CREATE INDEX IF NOT EXISTS profiles_subscription_tier_idx ON public.profiles (subscription_tier);
CREATE INDEX IF NOT EXISTS profiles_subscription_status_idx ON public.profiles (subscription_status);

-- Add updated_at trigger function
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER update_pods_updated_at
BEFORE UPDATE ON public.pods
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_templates_updated_at
BEFORE UPDATE ON public.templates
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_profiles_updated_at
BEFORE UPDATE ON public.profiles
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Create function to handle user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, first_name, last_name, avatar_url, company)
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data->>'firstName',
    NEW.raw_user_meta_data->>'lastName',
    NEW.raw_user_meta_data->>'avatar_url',
    NEW.raw_user_meta_data->>'company'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create profile for new users
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to handle user updates
CREATE OR REPLACE FUNCTION public.handle_user_update()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE public.profiles
  SET
    first_name = NEW.raw_user_meta_data->>'firstName',
    last_name = NEW.raw_user_meta_data->>'lastName',
    avatar_url = NEW.raw_user_meta_data->>'avatar_url',
    company = NEW.raw_user_meta_data->>'company',
    updated_at = now()
  WHERE id = NEW.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically update profile when user is updated
CREATE TRIGGER on_auth_user_updated
  AFTER UPDATE ON auth.users
  FOR EACH ROW
  WHEN (OLD.raw_user_meta_data IS DISTINCT FROM NEW.raw_user_meta_data)
  EXECUTE FUNCTION public.handle_user_update();
