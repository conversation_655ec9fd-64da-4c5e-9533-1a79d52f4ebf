-- Remove subscription-related columns from profiles table
-- This migration removes subscription_tier and subscription_status columns

-- Drop indexes first
DROP INDEX IF EXISTS public.profiles_subscription_tier_idx;
DROP INDEX IF EXISTS public.profiles_subscription_status_idx;

-- Drop the subscription columns
ALTER TABLE public.profiles 
DROP COLUMN IF EXISTS subscription_tier,
DROP COLUMN IF EXISTS subscription_status;
