-- Seed data for templates
INSERT INTO public.templates (id, name, description, icon_name, tags, category, price, github, website)
VALUES
    (
        'beetlehr',
        'BeetleHR',
        'Open source HR management system with employee management, attendance, payroll and more',
        'Users',
        ARRAY['HR', 'Management'],
        'Business',
        20.00,
        'https://github.com/beetlehr/beetlehr',
        'https://beetlehr.com'
    ),
    (
        'n8n',
        'n8n',
        'Workflow automation tool with 200+ integrations. Similar to Zapier',
        'Workflow',
        ARRAY['Automation', 'Integration'],
        'Productivity',
        20.00,
        'https://github.com/n8n-io/n8n',
        'https://n8n.io'
    ),
    (
        'wordpress',
        'WordPress',
        'Popular CMS for blogs and websites with thousands of plugins and themes',
        'Globe',
        ARRAY['CMS', 'Blog', 'Website'],
        'Web',
        15.00,
        'https://github.com/WordPress/WordPress',
        'https://wordpress.org'
    ),
    (
        'nextcloud',
        'Nextcloud',
        'Self-hosted productivity platform with file sharing, calendar, contacts and more',
        'Cloud',
        ARRAY['Storage', 'Productivity', 'Collaboration'],
        'Productivity',
        25.00,
        'https://github.com/nextcloud/server',
        'https://nextcloud.com'
    ),
    (
        'ghost',
        'Ghost',
        'Modern publishing platform for professional bloggers and creators',
        'FileText',
        ARRAY['Blog', 'CMS', 'Publishing'],
        'Web',
        20.00,
        'https://github.com/TryGhost/Ghost',
        'https://ghost.org'
    ),
    (
        'gitea',
        'Gitea',
        'Lightweight self-hosted Git service similar to GitHub',
        'GitBranch',
        ARRAY['Git', 'Development', 'Collaboration'],
        'Development',
        15.00,
        'https://github.com/go-gitea/gitea',
        'https://gitea.io'
    ),
    (
        'mattermost',
        'Mattermost',
        'Open source messaging platform similar to Slack',
        'MessageSquare',
        ARRAY['Communication', 'Collaboration', 'Chat'],
        'Communication',
        20.00,
        'https://github.com/mattermost/mattermost-server',
        'https://mattermost.com'
    ),
    (
        'plausible',
        'Plausible Analytics',
        'Simple, privacy-friendly alternative to Google Analytics',
        'BarChart',
        ARRAY['Analytics', 'Privacy', 'Web'],
        'Analytics',
        15.00,
        'https://github.com/plausible/analytics',
        'https://plausible.io'
    )
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    icon_name = EXCLUDED.icon_name,
    tags = EXCLUDED.tags,
    category = EXCLUDED.category,
    price = EXCLUDED.price,
    github = EXCLUDED.github,
    website = EXCLUDED.website,
    updated_at = now();
