# Setting Up Google OAuth for SumoPod

This guide will walk you through the process of setting up Google OAuth for your SumoPod application.

## 1. Create a Google Cloud Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Navigate to "APIs & Services" > "OAuth consent screen"
4. Select "External" user type and click "Create"
5. Fill in the required information:
   - App name: SumoPod
   - User support email: Your email
   - Developer contact information: Your email
6. Click "Save and Continue"
7. Skip adding scopes and click "Save and Continue"
8. Add test users if needed and click "Save and Continue"
9. Review your settings and click "Back to Dashboard"

## 2. Create OAuth Credentials

1. In the Google Cloud Console, navigate to "APIs & Services" > "Credentials"
2. Click "Create Credentials" and select "OAuth client ID"
3. Select "Web application" as the application type
4. Name: SumoPod Web Client
5. Add authorized JavaScript origins:
   - `https://dhsrwbufpdvuptdzeieo.supabase.co` (your Supabase project URL)
   - `http://localhost:5173` (for local development)
6. Add authorized redirect URIs:
   - `https://dhsrwbufpdvuptdzeieo.supabase.co/auth/v1/callback` (your Supabase project URL)
   - `http://localhost:5173/auth/v1/callback` (for local development)
7. Click "Create"
8. Note down the Client ID and Client Secret

## 3. Configure Supabase Auth

1. Go to your [Supabase Dashboard](https://app.supabase.com/)
2. Select your project
3. Navigate to "Authentication" > "Providers"
4. Find "Google" in the list and click "Edit"
5. Toggle "Enable Google OAuth" to ON
6. Enter the Client ID and Client Secret from the previous step
7. Save the changes

## 4. Test the Integration

1. Run your application locally or deploy it
2. Try signing in with Google
3. You should be redirected to Google's authentication page
4. After successful authentication, you should be redirected back to your application

## Troubleshooting

### Common Issues:

1. **Redirect URI Mismatch**: Ensure that the redirect URIs in Google Cloud Console match exactly with what Supabase expects.

2. **CORS Issues**: Make sure you've added all necessary origins to the authorized JavaScript origins list.

3. **Invalid Client ID or Secret**: Double-check that you've copied the correct values from Google Cloud Console to Supabase.

4. **OAuth Consent Screen Not Configured**: Ensure you've completed all steps in the OAuth consent screen setup.

5. **API Not Enabled**: You might need to enable the Google+ API or People API in your Google Cloud project.

If you encounter any issues, check the browser console for error messages and refer to the [Supabase Authentication documentation](https://supabase.com/docs/guides/auth/social-login/auth-google) for more details.
