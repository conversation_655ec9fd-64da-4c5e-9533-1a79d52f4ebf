import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
  build: {
    // Increase the chunk size warning limit
    chunkSizeWarningLimit: 1000, // 1000 kB
    rollupOptions: {
      output: {
        // Manual chunking configuration
        manualChunks: {
          // Vendor chunk for third-party libraries
          vendor: [
            'react',
            'react-dom',
            'react-router-dom',
            '@supabase/supabase-js'
          ],
          // UI components chunk
          ui: [
            'lucide-react'
          ],
          // Auth related functionality
          auth: [
            './src/context/AuthContext.tsx',
            './src/context/ProfileContext.tsx',
            './src/components/auth/ProtectedRoute.tsx'
          ],
          // Dashboard layout and components
          dashboard: [
            './src/components/layouts/DashboardLayout.tsx',
            './src/components/dashboard/Sidebar.tsx',
            './src/components/dashboard/DashboardHeader.tsx'
          ]
        }
      }
    }
  }
});
